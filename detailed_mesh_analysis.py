#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的网格分析脚本
深入分析两个TS文件的几何特性，理解为什么GOCAD认为是闭合的而PyMeshLab不认为
"""

import os
import sys
import numpy as np
import re
from datetime import datetime
import traceback

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def read_tsurf_data(file_path):
    """读取TS文件数据"""
    log_message(f"读取TS文件: {os.path.basename(file_path)}")
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                for line in file:
                    line = line.strip()
                    if not line:
                        continue
                    
                    if 'VRTX' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x]
                            if len(nums) >= 4:
                                vertex_id = int(nums[0])
                                x, y, z = float(nums[1]), float(nums[2]), float(nums[3])
                                vrtx.append([x, y, z])
                                vrtx_map[vertex_id] = current_idx
                                current_idx += 1
                        except:
                            continue
                    
                    elif 'TRGL' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x and x.isdigit()]
                            if len(nums) == 3:
                                v1 = vrtx_map[int(nums[0])]
                                v2 = vrtx_map[int(nums[1])]
                                v3 = vrtx_map[int(nums[2])]
                                trgl.append([v1, v2, v3])
                        except:
                            continue
            
            if len(vrtx) > 0 and len(trgl) > 0:
                log_message(f"  成功: {len(vrtx)} 顶点, {len(trgl)} 面")
                return np.array(vrtx), np.array(trgl)
                
        except:
            continue
    
    return np.array([]), np.array([])

def analyze_mesh_topology(vertices, faces, mesh_name):
    """详细分析网格拓扑结构"""
    log_message(f"\n=== {mesh_name} 拓扑分析 ===")
    
    # 基本信息
    log_message(f"顶点数: {len(vertices)}")
    log_message(f"面数: {len(faces)}")
    
    # 构建边字典
    edge_count = {}
    edge_faces = {}  # 记录每条边属于哪些面
    
    for face_idx, face in enumerate(faces):
        edges = [
            tuple(sorted([face[0], face[1]])),
            tuple(sorted([face[1], face[2]])),
            tuple(sorted([face[2], face[0]]))
        ]
        
        for edge in edges:
            if edge in edge_count:
                edge_count[edge] += 1
                edge_faces[edge].append(face_idx)
            else:
                edge_count[edge] = 1
                edge_faces[edge] = [face_idx]
    
    # 分析边的类型
    boundary_edges = []
    manifold_edges = []
    non_manifold_edges = []
    
    for edge, count in edge_count.items():
        if count == 1:
            boundary_edges.append(edge)
        elif count == 2:
            manifold_edges.append(edge)
        else:
            non_manifold_edges.append(edge)
    
    total_edges = len(edge_count)
    boundary_count = len(boundary_edges)
    manifold_count = len(manifold_edges)
    non_manifold_count = len(non_manifold_edges)
    
    log_message(f"总边数: {total_edges}")
    log_message(f"边界边: {boundary_count}")
    log_message(f"流形边: {manifold_count}")
    log_message(f"非流形边: {non_manifold_count}")
    
    # 欧拉特征数检查
    euler_char = len(vertices) - total_edges + len(faces)
    log_message(f"欧拉特征数 (V-E+F): {euler_char}")
    
    # 理论上闭合网格的欧拉特征数应该是2（对于球面拓扑）
    if euler_char == 2:
        log_message("  -> 符合闭合球面拓扑")
    else:
        log_message(f"  -> 不符合简单闭合拓扑 (期望=2)")
    
    # 判断闭合性
    is_topologically_closed = boundary_count == 0 and non_manifold_count == 0
    log_message(f"拓扑闭合: {'是' if is_topologically_closed else '否'}")
    
    # 如果有边界边，分析边界结构
    if boundary_count > 0:
        log_message(f"\n边界边分析:")
        log_message(f"  边界边数量: {boundary_count}")
        
        # 尝试找到边界环
        boundary_loops = find_boundary_loops(boundary_edges, vertices)
        log_message(f"  边界环数量: {len(boundary_loops)}")
        for i, loop in enumerate(boundary_loops):
            log_message(f"    环 {i+1}: {len(loop)} 条边")
    
    # 如果有非流形边，详细分析
    if non_manifold_count > 0:
        log_message(f"\n非流形边分析:")
        for i, edge in enumerate(non_manifold_edges[:5]):  # 只显示前5个
            count = edge_count[edge]
            log_message(f"  边 {edge}: 被 {count} 个面共享")
        if non_manifold_count > 5:
            log_message(f"  ... 还有 {non_manifold_count - 5} 条非流形边")
    
    return {
        'vertices': len(vertices),
        'faces': len(faces),
        'edges': total_edges,
        'boundary_edges': boundary_count,
        'manifold_edges': manifold_count,
        'non_manifold_edges': non_manifold_count,
        'euler_characteristic': euler_char,
        'is_topologically_closed': is_topologically_closed
    }

def find_boundary_loops(boundary_edges, vertices):
    """找到边界环"""
    # 构建边界边的邻接关系
    edge_graph = {}
    for edge in boundary_edges:
        v1, v2 = edge
        if v1 not in edge_graph:
            edge_graph[v1] = []
        if v2 not in edge_graph:
            edge_graph[v2] = []
        edge_graph[v1].append(v2)
        edge_graph[v2].append(v1)
    
    # 寻找环
    loops = []
    visited_edges = set()
    
    for start_vertex in edge_graph:
        if start_vertex in [v for loop in loops for v in loop]:
            continue
        
        # 尝试从这个顶点开始找环
        current_vertex = start_vertex
        loop = [current_vertex]
        
        while True:
            neighbors = [v for v in edge_graph.get(current_vertex, []) 
                        if tuple(sorted([current_vertex, v])) not in visited_edges]
            
            if not neighbors:
                break
            
            next_vertex = neighbors[0]
            visited_edges.add(tuple(sorted([current_vertex, next_vertex])))
            
            if next_vertex == start_vertex:
                # 找到了一个环
                loops.append(loop)
                break
            
            loop.append(next_vertex)
            current_vertex = next_vertex
            
            if len(loop) > len(boundary_edges):  # 防止无限循环
                break
    
    return loops

def check_with_pyvista(vertices, faces, mesh_name):
    """使用PyVista检查网格"""
    try:
        import pyvista as pv
        
        log_message(f"\n=== {mesh_name} PyVista分析 ===")
        
        # 创建PyVista网格
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        
        mesh = pv.PolyData(vertices, np.array(faces_with_header))
        
        log_message(f"PyVista网格: {mesh.n_points} 顶点, {mesh.n_cells} 面")
        
        # 检查边界
        edges = mesh.extract_feature_edges(boundary_edges=True, 
                                          non_manifold_edges=False, 
                                          manifold_edges=False)
        
        boundary_edge_count = edges.n_cells if edges.n_cells > 0 else 0
        log_message(f"PyVista边界边数: {boundary_edge_count}")
        
        # 检查非流形边
        non_manifold_edges = mesh.extract_feature_edges(boundary_edges=False,
                                                        non_manifold_edges=True,
                                                        manifold_edges=False)
        
        non_manifold_count = non_manifold_edges.n_cells if non_manifold_edges.n_cells > 0 else 0
        log_message(f"PyVista非流形边数: {non_manifold_count}")
        
        is_closed = boundary_edge_count == 0 and non_manifold_count == 0
        log_message(f"PyVista判断闭合: {'是' if is_closed else '否'}")
        
        # 尝试计算体积
        if is_closed:
            try:
                volume = mesh.volume
                log_message(f"体积: {volume:.6f}")
            except Exception as e:
                log_message(f"体积计算失败: {e}")
        
        return {
            'boundary_edges': boundary_edge_count,
            'non_manifold_edges': non_manifold_count,
            'is_closed': is_closed
        }
        
    except ImportError:
        log_message("PyVista未安装")
        return None
    except Exception as e:
        log_message(f"PyVista分析失败: {e}")
        return None

def check_with_pymeshlab(vertices, faces, mesh_name):
    """使用PyMeshLab检查网格"""
    try:
        import pymeshlab
        
        log_message(f"\n=== {mesh_name} PyMeshLab分析 ===")
        
        # 创建临时OBJ文件
        temp_obj = f"temp_{mesh_name.replace(' ', '_')}.obj"
        with open(temp_obj, 'w') as f:
            for v in vertices:
                f.write(f"v {v[0]:.12f} {v[1]:.12f} {v[2]:.12f}\n")
            for face in faces:
                f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
        
        # 加载到PyMeshLab
        ms = pymeshlab.MeshSet()
        ms.load_new_mesh(temp_obj)
        
        mesh = ms.current_mesh()
        log_message(f"PyMeshLab网格: {len(mesh.vertex_matrix())} 顶点, {len(mesh.face_matrix())} 面")
        
        # 尝试获取网格信息
        try:
            bbox = mesh.bounding_box()
            log_message(f"边界框: {bbox}")
        except Exception as e:
            log_message(f"边界框获取失败: {e}")
        
        # 尝试布尔运算测试（自相交）
        try:
            ms_test = pymeshlab.MeshSet()
            ms_test.load_new_mesh(temp_obj)
            ms_test.load_new_mesh(temp_obj)  # 加载同一个网格两次
            
            # 尝试并集运算（应该返回原网格如果是水密的）
            ms_test.generate_boolean_union(first_mesh=0, second_mesh=1)
            log_message("PyMeshLab布尔运算测试: 成功")
            
            # 删除第二个网格
            ms_test.set_current_mesh(1)
            ms_test.delete_current_mesh()
            
            result_mesh = ms_test.current_mesh()
            result_vertices = len(result_mesh.vertex_matrix())
            result_faces = len(result_mesh.face_matrix())
            log_message(f"布尔运算结果: {result_vertices} 顶点, {result_faces} 面")
            
        except Exception as e:
            log_message(f"PyMeshLab布尔运算测试: 失败 - {e}")
        
        # 清理临时文件
        if os.path.exists(temp_obj):
            os.remove(temp_obj)
        
        return True
        
    except ImportError:
        log_message("PyMeshLab未安装")
        return None
    except Exception as e:
        log_message(f"PyMeshLab分析失败: {e}")
        return None

def main():
    """主函数"""
    log_message("=== 详细网格分析 ===")
    log_message("分析两个TS文件的几何特性，理解闭合性差异")
    
    # 检查输入文件
    file1 = "1_05-J2a_1.ts"
    file2 = "dixing2507101.ts"
    
    if not os.path.exists(file1) or not os.path.exists(file2):
        log_message("错误: 找不到输入文件")
        return False
    
    try:
        # 分析第一个文件
        log_message(f"\n{'='*60}")
        log_message(f"分析文件: {file1}")
        log_message(f"{'='*60}")
        
        vertices1, faces1 = read_tsurf_data(file1)
        if len(vertices1) > 0:
            topo_result1 = analyze_mesh_topology(vertices1, faces1, "文件1")
            pv_result1 = check_with_pyvista(vertices1, faces1, "文件1")
            ml_result1 = check_with_pymeshlab(vertices1, faces1, "文件1")
        
        # 分析第二个文件
        log_message(f"\n{'='*60}")
        log_message(f"分析文件: {file2}")
        log_message(f"{'='*60}")
        
        vertices2, faces2 = read_tsurf_data(file2)
        if len(vertices2) > 0:
            topo_result2 = analyze_mesh_topology(vertices2, faces2, "文件2")
            pv_result2 = check_with_pyvista(vertices2, faces2, "文件2")
            ml_result2 = check_with_pymeshlab(vertices2, faces2, "文件2")
        
        # 总结分析
        log_message(f"\n{'='*60}")
        log_message("=== 分析总结 ===")
        log_message(f"{'='*60}")
        
        log_message(f"\n{file1}:")
        log_message(f"  拓扑闭合: {topo_result1['is_topologically_closed']}")
        log_message(f"  边界边: {topo_result1['boundary_edges']}")
        log_message(f"  非流形边: {topo_result1['non_manifold_edges']}")
        log_message(f"  欧拉特征数: {topo_result1['euler_characteristic']}")
        if pv_result1:
            log_message(f"  PyVista判断闭合: {pv_result1['is_closed']}")
        
        log_message(f"\n{file2}:")
        log_message(f"  拓扑闭合: {topo_result2['is_topologically_closed']}")
        log_message(f"  边界边: {topo_result2['boundary_edges']}")
        log_message(f"  非流形边: {topo_result2['non_manifold_edges']}")
        log_message(f"  欧拉特征数: {topo_result2['euler_characteristic']}")
        if pv_result2:
            log_message(f"  PyVista判断闭合: {pv_result2['is_closed']}")
        
        return True
        
    except Exception as e:
        log_message(f"分析失败: {e}")
        log_message(traceback.format_exc())
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log_message("程序被用户中断")
        sys.exit(1)
    except Exception as e:
        log_message(f"程序异常退出: {e}")
        sys.exit(1)
