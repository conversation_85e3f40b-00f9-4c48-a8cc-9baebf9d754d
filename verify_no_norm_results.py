#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证不进行坐标标准化的布尔运算结果
"""

import os
import sys
import numpy as np
from datetime import datetime

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_mesh_closure_manual(obj_file):
    """手动检查网格闭合性"""
    try:
        log_message(f"检查: {os.path.basename(obj_file)}")
        
        vertices = []
        faces = []
        
        # 读取OBJ文件
        with open(obj_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('v '):
                    parts = line.split()
                    if len(parts) >= 4:
                        vertices.append([float(parts[1]), float(parts[2]), float(parts[3])])
                elif line.startswith('f '):
                    parts = line.split()
                    if len(parts) >= 4:
                        face = []
                        for i in range(1, 4):
                            vertex_idx = int(parts[i].split('/')[0]) - 1
                            face.append(vertex_idx)
                        faces.append(face)
        
        vertices = np.array(vertices)
        faces = np.array(faces)
        
        log_message(f"  网格: {len(vertices)} 顶点, {len(faces)} 面")
        
        # 检查坐标范围
        if len(vertices) > 0:
            log_message(f"  坐标范围: X=[{np.min(vertices[:,0]):.1f}, {np.max(vertices[:,0]):.1f}]")
            log_message(f"  坐标范围: Y=[{np.min(vertices[:,1]):.1f}, {np.max(vertices[:,1]):.1f}]")
            log_message(f"  坐标范围: Z=[{np.min(vertices[:,2]):.1f}, {np.max(vertices[:,2]):.1f}]")
        
        # 构建边字典
        edge_count = {}
        
        for face in faces:
            edges = [
                tuple(sorted([face[0], face[1]])),
                tuple(sorted([face[1], face[2]])),
                tuple(sorted([face[2], face[0]]))
            ]
            
            for edge in edges:
                if edge in edge_count:
                    edge_count[edge] += 1
                else:
                    edge_count[edge] = 1
        
        # 分析边的共享情况
        boundary_edges = []
        manifold_edges = []
        non_manifold_edges = []
        
        for edge, count in edge_count.items():
            if count == 1:
                boundary_edges.append(edge)
            elif count == 2:
                manifold_edges.append(edge)
            else:
                non_manifold_edges.append(edge)
        
        total_edges = len(edge_count)
        boundary_count = len(boundary_edges)
        manifold_count = len(manifold_edges)
        non_manifold_count = len(non_manifold_edges)
        
        log_message(f"  总边数: {total_edges}")
        log_message(f"  边界边: {boundary_count}")
        log_message(f"  流形边: {manifold_count}")
        log_message(f"  非流形边: {non_manifold_count}")
        
        # 欧拉特征数
        euler_char = len(vertices) - total_edges + len(faces)
        log_message(f"  欧拉特征数: {euler_char}")
        
        # 判断闭合性
        is_closed = boundary_count == 0 and non_manifold_count == 0
        
        if is_closed:
            log_message(f"  ✅ 网格是闭合的！")
        else:
            log_message(f"  ❌ 网格不闭合")
            if boundary_count > 0:
                log_message(f"    - 有 {boundary_count} 条边界边")
            if non_manifold_count > 0:
                log_message(f"    - 有 {non_manifold_count} 条非流形边")
        
        return {
            'file': os.path.basename(obj_file),
            'vertices': len(vertices),
            'faces': len(faces),
            'is_closed': is_closed,
            'boundary_edges': boundary_count,
            'euler_characteristic': euler_char
        }
        
    except Exception as e:
        log_message(f"检查失败: {e}")
        return None

def main():
    """主函数"""
    log_message("=== 验证不进行坐标标准化的布尔运算结果 ===")
    
    # 查找结果目录
    result_dirs = [d for d in os.listdir('.') if d.startswith('no_norm_results_')]
    
    if not result_dirs:
        log_message("错误: 找不到无标准化结果目录")
        return False
    
    # 使用最新的结果目录
    result_dir = sorted(result_dirs)[-1]
    log_message(f"检查目录: {result_dir}")
    
    # 查找OBJ文件
    obj_files = []
    for file in os.listdir(result_dir):
        if file.endswith('.obj'):
            obj_files.append(os.path.join(result_dir, file))
    
    if not obj_files:
        log_message("错误: 找不到OBJ文件")
        return False
    
    log_message(f"找到 {len(obj_files)} 个OBJ文件")
    
    all_closed = True
    
    for obj_file in sorted(obj_files):
        log_message(f"\n{'='*60}")
        
        # 检查闭合性
        result = check_mesh_closure_manual(obj_file)
        
        if result and not result['is_closed']:
            all_closed = False
    
    # 总结
    log_message(f"\n{'='*60}")
    log_message("=== 验证总结 ===")
    
    if all_closed:
        log_message("🎉 所有不进行坐标标准化的布尔运算结果都是闭合的！")
        log_message("这证明了：")
        log_message("1. 坐标标准化不是必要的")
        log_message("2. 四面体网格化本身就解决了PyMeshLab的要求")
        log_message("3. 原始坐标系得到了完美保持")
    else:
        log_message("⚠️  部分结果可能不完全闭合。")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log_message("程序被用户中断")
        sys.exit(1)
    except Exception as e:
        log_message(f"程序异常退出: {e}")
        sys.exit(1)
