#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确的TS到OBJ转换和PyMeshLab布尔运算
使用更高精度的转换方法，避免信息丢失
"""

import os
import sys
import numpy as np
import re
from datetime import datetime
import traceback

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def read_tsurf_data_precise(file_path):
    """
    精确读取TS文件数据，保持最高精度
    """
    log_message(f"精确读取TS文件: {os.path.basename(file_path)}")
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                for line_num, line in enumerate(file, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    if 'VRTX' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x]
                            if len(nums) >= 4:
                                vertex_id = int(nums[0])
                                # 使用更高精度的浮点数
                                x = np.float64(nums[1])
                                y = np.float64(nums[2])
                                z = np.float64(nums[3])
                                vrtx.append([x, y, z])
                                vrtx_map[vertex_id] = current_idx
                                current_idx += 1
                        except (ValueError, IndexError) as e:
                            log_message(f"处理顶点时出错，行 {line_num}: {e}")
                            continue
                    
                    elif 'TRGL' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x and x.isdigit()]
                            if len(nums) == 3:
                                v1 = vrtx_map[int(nums[0])]
                                v2 = vrtx_map[int(nums[1])]
                                v3 = vrtx_map[int(nums[2])]
                                trgl.append([v1, v2, v3])
                        except (ValueError, IndexError, KeyError) as e:
                            log_message(f"处理三角面时出错，行 {line_num}: {e}")
                            continue
            
            if len(vrtx) > 0 and len(trgl) > 0:
                log_message(f"  成功: {len(vrtx)} 顶点, {len(trgl)} 面")
                return np.array(vrtx, dtype=np.float64), np.array(trgl)
                
        except UnicodeDecodeError:
            continue
        except Exception as e:
            log_message(f"读取文件时发生意外错误: {e}")
            continue
    
    return np.array([]), np.array([])

def normalize_coordinates_precise(vertices1, vertices2):
    """
    精确的坐标标准化，使用更高精度
    """
    log_message("执行精确坐标标准化...")
    
    all_vertices = np.vstack([vertices1, vertices2])
    min_coords = np.min(all_vertices, axis=0)
    
    # 使用更保守的偏移策略，保持更多精度
    vertices1_norm = vertices1 - min_coords
    vertices2_norm = vertices2 - min_coords
    
    log_message(f"坐标偏移量: X={min_coords[0]:.8f}, Y={min_coords[1]:.8f}, Z={min_coords[2]:.8f}")
    return vertices1_norm, vertices2_norm, min_coords

def write_obj_file_precise(file_path, vertices, faces):
    """
    写入高精度OBJ文件
    """
    try:
        with open(file_path, 'w') as f:
            # 写入顶点，使用更高精度
            for v in vertices:
                f.write(f"v {v[0]:.12f} {v[1]:.12f} {v[2]:.12f}\n")
            
            # 写入面（OBJ索引从1开始）
            for face in faces:
                f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
        
        return True
    except Exception as e:
        log_message(f"写入OBJ文件失败: {e}")
        return False

def write_ts_file_precise(file_path, vertices, faces, name="result"):
    """
    写入高精度TS文件
    """
    try:
        with open(file_path, 'w') as f:
            # 写入TS文件头，模仿原始格式
            f.write(f"GOCAD TSurf 1\n")
            f.write(f"HEADER {{\n")
            f.write(f"name: {name}\n")
            f.write(f"}}\n")
            f.write("TFACE\n")
            
            # 写入顶点，使用高精度
            for i, v in enumerate(vertices):
                f.write(f"VRTX {i+1} {v[0]:.12f} {v[1]:.12f} {v[2]:.12f}\n")
            
            # 写入面
            for face in faces:
                f.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            f.write("END\n")
        
        return True
    except Exception as e:
        log_message(f"写入TS文件失败: {e}")
        return False

def try_pymeshlab_boolean_precise(file1, file2, output_dir, coord_offset):
    """
    使用精确转换的OBJ文件进行PyMeshLab布尔运算
    """
    try:
        import pymeshlab
        
        log_message("=== PyMeshLab精确布尔运算 ===")
        
        results = {}
        operations = [
            ("intersection", "交集"),
            ("difference", "差集"),
            ("union", "并集")
        ]
        
        for op_type, op_name in operations:
            try:
                log_message(f"\n执行{op_name}运算...")
                
                # 创建新的MeshSet
                ms = pymeshlab.MeshSet()
                ms.load_new_mesh(file1)
                ms.load_new_mesh(file2)
                
                # 记录输入网格信息
                ms.set_current_mesh(0)
                m1 = ms.current_mesh()
                log_message(f"  网格1: 顶点={len(m1.vertex_matrix())}, 面={len(m1.face_matrix())}")
                
                ms.set_current_mesh(1)
                m2 = ms.current_mesh()
                log_message(f"  网格2: 顶点={len(m2.vertex_matrix())}, 面={len(m2.face_matrix())}")
                
                # 尝试不同的布尔运算参数
                log_message("  尝试标准参数...")
                ms.set_current_mesh(0)
                
                try:
                    if op_type == "intersection":
                        ms.generate_boolean_intersection(first_mesh=0, second_mesh=1)
                    elif op_type == "difference":
                        ms.generate_boolean_difference(first_mesh=0, second_mesh=1)
                    elif op_type == "union":
                        ms.generate_boolean_union(first_mesh=0, second_mesh=1)
                    
                    # 删除第二个网格，只保留结果
                    ms.set_current_mesh(1)
                    ms.delete_current_mesh()
                    
                    # 检查结果
                    result_mesh = ms.current_mesh()
                    vertex_count = len(result_mesh.vertex_matrix())
                    face_count = len(result_mesh.face_matrix())
                    
                    log_message(f"  {op_name}结果: 顶点={vertex_count}, 面={face_count}")
                    
                    if vertex_count > 0 and face_count > 0:
                        # 保存结果
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        obj_file = os.path.join(output_dir, f"precise_{op_type}_{timestamp}.obj")
                        ts_file = os.path.join(output_dir, f"precise_{op_type}_{timestamp}.ts")
                        
                        # 保存OBJ格式
                        ms.save_current_mesh(obj_file)
                        
                        # 读取结果并转换为TS格式
                        vertices, faces = read_obj_file(obj_file)
                        if len(vertices) > 0:
                            # 还原坐标
                            vertices_restored = vertices + coord_offset
                            write_ts_file_precise(ts_file, vertices_restored, faces, f"precise_{op_type}_result")
                            log_message(f"  ✓ 已保存{op_name}结果: {os.path.basename(ts_file)}")
                            
                            results[op_type] = {
                                'success': True,
                                'obj_file': obj_file,
                                'ts_file': ts_file,
                                'vertices': vertex_count,
                                'faces': face_count
                            }
                    else:
                        log_message(f"  ✗ {op_name}结果为空")
                        results[op_type] = {'success': False, 'reason': '结果为空'}
                        
                except Exception as e:
                    log_message(f"  ✗ {op_name}运算失败: {e}")
                    
                    # 如果标准参数失败，尝试其他策略
                    log_message("  尝试替代策略...")
                    
                    # 重新加载网格
                    ms = pymeshlab.MeshSet()
                    ms.load_new_mesh(file1)
                    ms.load_new_mesh(file2)
                    
                    # 尝试基础清理
                    ms.set_current_mesh(0)
                    ms.meshing_remove_duplicate_vertices()
                    ms.meshing_remove_duplicate_faces()
                    
                    ms.set_current_mesh(1)
                    ms.meshing_remove_duplicate_vertices()
                    ms.meshing_remove_duplicate_faces()
                    
                    # 再次尝试布尔运算
                    try:
                        ms.set_current_mesh(0)
                        if op_type == "intersection":
                            ms.generate_boolean_intersection(first_mesh=0, second_mesh=1)
                        elif op_type == "difference":
                            ms.generate_boolean_difference(first_mesh=0, second_mesh=1)
                        elif op_type == "union":
                            ms.generate_boolean_union(first_mesh=0, second_mesh=1)
                        
                        ms.set_current_mesh(1)
                        ms.delete_current_mesh()
                        
                        result_mesh = ms.current_mesh()
                        vertex_count = len(result_mesh.vertex_matrix())
                        face_count = len(result_mesh.face_matrix())
                        
                        if vertex_count > 0 and face_count > 0:
                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                            obj_file = os.path.join(output_dir, f"precise_cleaned_{op_type}_{timestamp}.obj")
                            ts_file = os.path.join(output_dir, f"precise_cleaned_{op_type}_{timestamp}.ts")
                            
                            ms.save_current_mesh(obj_file)
                            
                            vertices, faces = read_obj_file(obj_file)
                            if len(vertices) > 0:
                                vertices_restored = vertices + coord_offset
                                write_ts_file_precise(ts_file, vertices_restored, faces, f"precise_cleaned_{op_type}_result")
                                log_message(f"  ✓ 清理后{op_name}成功: {os.path.basename(ts_file)}")
                                
                                results[op_type] = {
                                    'success': True,
                                    'obj_file': obj_file,
                                    'ts_file': ts_file,
                                    'vertices': vertex_count,
                                    'faces': face_count
                                }
                        else:
                            results[op_type] = {'success': False, 'reason': '清理后仍为空'}
                    except Exception as e2:
                        log_message(f"  ✗ 清理后{op_name}仍失败: {e2}")
                        results[op_type] = {'success': False, 'reason': f'标准和清理方法都失败: {e}, {e2}'}
                    
            except Exception as e:
                log_message(f"  ✗ {op_name}整体失败: {e}")
                results[op_type] = {'success': False, 'reason': str(e)}
        
        return results
        
    except ImportError:
        log_message("错误: PyMeshLab未安装")
        return {}
    except Exception as e:
        log_message(f"PyMeshLab精确布尔运算整体失败: {e}")
        return {}

def read_obj_file(file_path):
    """读取OBJ文件"""
    vertices = []
    faces = []
    
    try:
        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('v '):
                    parts = line.split()
                    if len(parts) >= 4:
                        vertices.append([float(parts[1]), float(parts[2]), float(parts[3])])
                elif line.startswith('f '):
                    parts = line.split()
                    if len(parts) >= 4:
                        face = []
                        for i in range(1, 4):
                            vertex_idx = int(parts[i].split('/')[0]) - 1
                            face.append(vertex_idx)
                        faces.append(face)
        
        return np.array(vertices), np.array(faces)
    except Exception as e:
        log_message(f"读取OBJ文件失败: {e}")
        return np.array([]), np.array([])

def main():
    """主函数"""
    log_message("=== 精确TS到OBJ转换和PyMeshLab布尔运算 ===")

    # 检查依赖
    try:
        import pymeshlab
        log_message("PyMeshLab已安装")
    except ImportError:
        log_message("错误: PyMeshLab未安装")
        return False

    # 检查输入文件
    file1 = "1_05-J2a_1.ts"
    file2 = "dixing2507101.ts"

    if not os.path.exists(file1) or not os.path.exists(file2):
        log_message("错误: 找不到输入文件")
        return False

    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"precise_results_{timestamp}"
    temp_dir = f"temp_precise_{timestamp}"

    try:
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(temp_dir, exist_ok=True)
        log_message(f"输出目录: {output_dir}")

        # 步骤1: 精确读取TS文件
        log_message("\n=== 步骤1: 精确读取TS文件 ===")
        vertices1, faces1 = read_tsurf_data_precise(file1)
        vertices2, faces2 = read_tsurf_data_precise(file2)

        if len(vertices1) == 0 or len(vertices2) == 0:
            log_message("错误: 无法读取输入文件")
            return False

        # 分析原始坐标
        log_message("原始坐标分析:")
        log_message(f"  文件1坐标范围: X=[{np.min(vertices1[:,0]):.8f}, {np.max(vertices1[:,0]):.8f}]")
        log_message(f"  文件1坐标范围: Y=[{np.min(vertices1[:,1]):.8f}, {np.max(vertices1[:,1]):.8f}]")
        log_message(f"  文件1坐标范围: Z=[{np.min(vertices1[:,2]):.8f}, {np.max(vertices1[:,2]):.8f}]")

        log_message(f"  文件2坐标范围: X=[{np.min(vertices2[:,0]):.8f}, {np.max(vertices2[:,0]):.8f}]")
        log_message(f"  文件2坐标范围: Y=[{np.min(vertices2[:,1]):.8f}, {np.max(vertices2[:,1]):.8f}]")
        log_message(f"  文件2坐标范围: Z=[{np.min(vertices2[:,2]):.8f}, {np.max(vertices2[:,2]):.8f}]")

        # 步骤2: 精确坐标标准化
        log_message("\n=== 步骤2: 精确坐标标准化 ===")
        vertices1_norm, vertices2_norm, coord_offset = normalize_coordinates_precise(vertices1, vertices2)

        # 验证标准化效果
        log_message("标准化后坐标分析:")
        log_message(f"  文件1标准化后: X=[{np.min(vertices1_norm[:,0]):.8f}, {np.max(vertices1_norm[:,0]):.8f}]")
        log_message(f"  文件2标准化后: X=[{np.min(vertices2_norm[:,0]):.8f}, {np.max(vertices2_norm[:,0]):.8f}]")

        # 步骤3: 创建精确的OBJ文件
        log_message("\n=== 步骤3: 创建精确OBJ文件 ===")
        temp_obj1 = os.path.join(temp_dir, "mesh1_precise.obj")
        temp_obj2 = os.path.join(temp_dir, "mesh2_precise.obj")

        write_obj_file_precise(temp_obj1, vertices1_norm, faces1)
        write_obj_file_precise(temp_obj2, vertices2_norm, faces2)
        log_message("已创建精确OBJ文件")

        # 步骤4: 执行PyMeshLab布尔运算
        log_message("\n=== 步骤4: PyMeshLab精确布尔运算 ===")
        results = try_pymeshlab_boolean_precise(temp_obj1, temp_obj2, output_dir, coord_offset)

        # 步骤5: 结果总结
        log_message("\n=== 结果总结 ===")
        success_count = 0

        for op_type in ["intersection", "difference", "union"]:
            if op_type in results and results[op_type]['success']:
                result = results[op_type]
                log_message(f"✓ {op_type}: 成功 (顶点={result['vertices']}, 面={result['faces']})")
                success_count += 1
            else:
                reason = results.get(op_type, {}).get('reason', '未执行')
                log_message(f"✗ {op_type}: 失败 - {reason}")

        # 清理临时文件
        try:
            import shutil
            shutil.rmtree(temp_dir)
        except:
            pass

        log_message(f"\n处理完成！成功: {success_count}/3")
        log_message(f"结果保存在: {output_dir}")

        if success_count > 0:
            log_message("\n🎉 精确方法成功！")
            log_message("使用高精度转换的PyMeshLab布尔运算成功。")
            log_message("这证明了精确的TS到OBJ转换是关键因素。")
        else:
            log_message("\n❌ 精确方法也失败了。")
            log_message("问题可能不仅仅是转换精度，还有其他因素。")

        return success_count > 0

    except Exception as e:
        log_message(f"程序执行失败: {e}")
        log_message(traceback.format_exc())
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log_message("程序被用户中断")
        sys.exit(1)
    except Exception as e:
        log_message(f"程序异常退出: {e}")
        sys.exit(1)
