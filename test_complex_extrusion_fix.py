#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试复杂挤出实体形态保持修复
验证高精度四面体化和预处理的效果
"""

import os
import sys
import numpy as np
import pyvista as pv
from datetime import datetime

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def create_complex_surface():
    """创建一个复杂的测试表面（类似实际TS面）"""
    # 创建一个有起伏的表面
    x = np.linspace(0, 2, 10)
    y = np.linspace(0, 2, 10)
    X, Y = np.meshgrid(x, y)
    
    # 添加一些起伏
    Z = 0.1 * np.sin(2 * np.pi * X) * np.cos(2 * np.pi * Y)
    
    # 创建表面网格
    surface = pv.StructuredGrid(X, Y, Z)
    surface = surface.extract_surface().triangulate()
    
    return surface

def create_high_quality_extruded_solid(surface_mesh, extrusion_vector, thickness):
    """创建高质量的挤出实体（模拟修复后的方法）"""
    try:
        log_message(f"创建高质量挤出实体，厚度: {thickness}")
        
        # 方法1: 使用PyVista的标准extrude方法
        try:
            extruded_solid = surface_mesh.extrude(extrusion_vector, capping=True)
            
            # 检查挤出结果的质量
            if extruded_solid.n_cells > 0 and extruded_solid.n_points > 0:
                log_message(f"标准挤出成功: {extruded_solid.n_points}个点, {extruded_solid.n_cells}个面")
                return extruded_solid
            else:
                raise ValueError("标准挤出结果为空")
                
        except Exception as e1:
            log_message(f"标准挤出失败: {str(e1)}")
            
            # 方法2: 手动创建挤出实体
            try:
                log_message("尝试手动创建挤出实体...")
                
                # 创建底面和顶面
                bottom_surface = surface_mesh.copy()
                top_surface = surface_mesh.copy()
                top_surface.points = top_surface.points + extrusion_vector
                
                # 简单合并方法
                extruded_solid = bottom_surface.merge(top_surface)
                
                log_message(f"手动挤出成功: {extruded_solid.n_points}个点, {extruded_solid.n_cells}个面")
                return extruded_solid
                
            except Exception as e2:
                log_message(f"手动挤出失败: {str(e2)}")
                raise e2
                
    except Exception as e:
        log_message(f"创建挤出实体失败: {str(e)}")
        raise e

def preprocess_mesh_for_tetrahedralization(mesh, mesh_name="网格"):
    """预处理网格以提高四面体化质量（模拟修复后的方法）"""
    try:
        log_message(f"预处理{mesh_name}以提高四面体化质量...")
        
        # 创建网格副本
        processed_mesh = mesh.copy()
        
        # 步骤1: 基本清理
        processed_mesh = processed_mesh.clean(tolerance=1e-6)
        log_message(f"  {mesh_name}基本清理完成")
        
        # 步骤2: 确保是三角形网格
        if not processed_mesh.is_all_triangles:
            processed_mesh = processed_mesh.triangulate()
            log_message(f"  {mesh_name}三角化完成")
        
        # 步骤3: 移除重复点
        processed_mesh = processed_mesh.clean(tolerance=1e-8)
        log_message(f"  {mesh_name}重复点移除完成")
        
        # 步骤4: 检查网格质量
        original_points = mesh.n_points
        original_cells = mesh.n_cells
        processed_points = processed_mesh.n_points
        processed_cells = processed_mesh.n_cells
        
        log_message(f"  {mesh_name}预处理结果:")
        log_message(f"    点数: {original_points} -> {processed_points}")
        log_message(f"    面数: {original_cells} -> {processed_cells}")
        
        return processed_mesh
        
    except Exception as e:
        log_message(f"预处理{mesh_name}失败: {str(e)}")
        return mesh  # 返回原始网格

def create_high_quality_tetrahedral_mesh(pyvista_mesh, mesh_name="网格"):
    """为挤出实体创建高精度四面体网格（模拟修复后的方法）"""
    try:
        log_message(f"为{mesh_name}创建高精度四面体网格...")
        
        # 从PyVista网格提取表面
        surface_mesh = pyvista_mesh.extract_surface()
        
        # 确保网格是三角形网格
        if not surface_mesh.is_all_triangles:
            surface_mesh = surface_mesh.triangulate()
        
        log_message(f"  {mesh_name}表面网格: {surface_mesh.n_points}个点, {surface_mesh.n_cells}个三角形")
        
        # 使用高质量的Delaunay四面体化
        log_message(f"  开始{mesh_name}高精度四面体化...")
        
        # 方法1: 使用保守的细化参数
        try:
            # 轻微细化以保持形状，但不过度细化
            refined_mesh = surface_mesh.subdivide(nsub=1, subfilter='linear')
            log_message(f"  {mesh_name}网格细化: {refined_mesh.n_points}个点, {refined_mesh.n_cells}个面")
            
            # 使用保守的Delaunay参数
            tet_mesh = refined_mesh.delaunay_3d(alpha=0, tol=1e-06, offset=1.0)
            
            if tet_mesh.n_cells > 0:
                log_message(f"  {mesh_name}细化四面体网格化成功: {tet_mesh.n_points}个点, {tet_mesh.n_cells}个四面体")
                return tet_mesh
            else:
                raise ValueError("细化四面体化结果为空")
                
        except Exception as e1:
            log_message(f"  {mesh_name}细化方法失败: {str(e1)}")
            
            # 方法2: 使用默认参数
            try:
                log_message(f"  尝试{mesh_name}默认四面体化...")
                tet_mesh = surface_mesh.delaunay_3d()
                
                if tet_mesh.n_cells > 0:
                    log_message(f"  {mesh_name}默认四面体网格化成功: {tet_mesh.n_points}个点, {tet_mesh.n_cells}个四面体")
                    return tet_mesh
                else:
                    raise ValueError("默认四面体化结果为空")
                    
            except Exception as e2:
                log_message(f"  {mesh_name}默认方法也失败: {str(e2)}")
                raise e2
        
    except Exception as e:
        log_message(f"  {mesh_name}高精度四面体网格化失败: {str(e)}")
        return None

def test_complex_extrusion_fix():
    """测试复杂挤出实体修复"""
    log_message("=== 测试复杂挤出实体形态保持修复 ===")
    
    # 创建复杂测试表面
    surface_mesh = create_complex_surface()
    log_message(f"创建复杂测试表面: {surface_mesh.n_points} 顶点, {surface_mesh.n_cells} 面")
    
    # 计算挤出方向
    surface_mesh = surface_mesh.compute_normals()
    normals = surface_mesh.point_normals
    avg_normal = np.mean(normals, axis=0)
    avg_normal = avg_normal / np.linalg.norm(avg_normal)
    
    thickness = 0.3
    extrusion_vector = avg_normal * thickness
    
    log_message(f"挤出方向: {avg_normal}, 厚度: {thickness}")
    
    # 使用修复后的方法创建挤出实体
    try:
        extruded_solid = create_high_quality_extruded_solid(surface_mesh, extrusion_vector, thickness)
        log_message(f"高质量挤出成功: {extruded_solid.n_points} 顶点, {extruded_solid.n_cells} 面")
        
        # 清理和优化
        extruded_solid_clean = extruded_solid.clean().triangulate()
        log_message(f"清理后: {extruded_solid_clean.n_points} 顶点, {extruded_solid_clean.n_cells} 面")
        
        # 保存原始挤出实体
        original_obj = "test_complex_extruded_original.obj"
        extruded_solid_clean.save(original_obj)
        log_message(f"✓ 保存原始挤出实体: {original_obj}")
        
        # 预处理挤出实体
        preprocessed_extruded_solid = preprocess_mesh_for_tetrahedralization(extruded_solid_clean, "挤出实体")
        
        # 使用修复后的高精度四面体化
        tet_mesh = create_high_quality_tetrahedral_mesh(preprocessed_extruded_solid, "挤出实体")
        
        if tet_mesh is not None:
            log_message(f"高精度四面体化成功: {tet_mesh.n_points} 顶点, {tet_mesh.n_cells} 四面体")
            
            # 从四面体网格提取表面
            surface_from_tet = tet_mesh.extract_surface()
            log_message(f"从四面体提取表面: {surface_from_tet.n_points} 顶点, {surface_from_tet.n_cells} 面")
            
            # 保存四面体化后的表面
            tet_surface_obj = "test_complex_extruded_from_tet.obj"
            surface_from_tet.save(tet_surface_obj)
            log_message(f"✓ 保存四面体化后表面: {tet_surface_obj}")
            
            # 比较形态差异
            original_bounds = extruded_solid_clean.bounds
            tet_bounds = surface_from_tet.bounds
            
            log_message("\n=== 复杂形态比较 ===")
            log_message(f"原始挤出实体边界: {original_bounds}")
            log_message(f"四面体化后边界: {tet_bounds}")
            
            # 计算边界差异
            bounds_diff = np.array(tet_bounds) - np.array(original_bounds)
            max_diff = np.max(np.abs(bounds_diff))
            
            log_message(f"最大边界差异: {max_diff:.6f}")
            
            # 清理测试文件
            try:
                os.remove(original_obj)
                os.remove(tet_surface_obj)
                log_message("✓ 测试文件已清理")
            except:
                pass
            
            if max_diff < 0.1:  # 10%的容差（复杂形状）
                log_message("✓ 复杂形态保持良好")
                return True
            else:
                log_message("✗ 复杂形态发生明显变化")
                return False
        else:
            log_message("✗ 高精度四面体化失败")
            return False
        
    except Exception as e:
        log_message(f"✗ 复杂挤出测试失败: {e}")
        return False

def main():
    """主函数"""
    log_message("开始测试复杂挤出实体形态保持修复...")
    
    complex_test = test_complex_extrusion_fix()
    
    if complex_test:
        log_message("\n🎉 复杂挤出实体形态保持修复测试成功！")
        log_message("✓ 高质量挤出实体创建")
        log_message("✓ 预处理提高四面体化质量")
        log_message("✓ 高精度四面体化保持形态")
        log_message("✓ 修复方案对复杂形状有效")
    else:
        log_message("\n❌ 复杂挤出实体形态保持修复测试失败")
    
    return complex_test

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        log_message(f"程序异常: {e}")
        sys.exit(1)
