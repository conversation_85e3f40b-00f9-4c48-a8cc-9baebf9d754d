#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真实TS文件测试挤出实体形态保持修复
"""

import os
import sys
import numpy as np
import pyvista as pv
from datetime import datetime

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def read_tsurf_data(file_path):
    """读取TSURF文件数据"""
    vertices = []
    faces = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        vertex_index_map = {}
        current_vertex_index = 0
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('VRTX'):
                parts = line.split()
                if len(parts) >= 5:
                    vertex_id = int(parts[1])
                    x, y, z = float(parts[2]), float(parts[3]), float(parts[4])
                    vertices.append([x, y, z])
                    vertex_index_map[vertex_id] = current_vertex_index
                    current_vertex_index += 1
            
            elif line.startswith('TRGL'):
                parts = line.split()
                if len(parts) >= 4:
                    v1, v2, v3 = int(parts[1]), int(parts[2]), int(parts[3])
                    if v1 in vertex_index_map and v2 in vertex_index_map and v3 in vertex_index_map:
                        face = [vertex_index_map[v1], vertex_index_map[v2], vertex_index_map[v3]]
                        faces.append(face)
        
        return np.array(vertices), np.array(faces)
    
    except Exception as e:
        log_message(f"读取TSURF文件失败: {e}")
        return np.array([]), np.array([])

def create_high_quality_extruded_solid(surface_mesh, extrusion_vector, thickness):
    """创建高质量的挤出实体"""
    try:
        log_message(f"创建高质量挤出实体，厚度: {thickness}")
        
        # 使用PyVista的标准extrude方法
        extruded_solid = surface_mesh.extrude(extrusion_vector, capping=True)
        
        if extruded_solid.n_cells > 0 and extruded_solid.n_points > 0:
            log_message(f"挤出成功: {extruded_solid.n_points}个点, {extruded_solid.n_cells}个面")
            return extruded_solid
        else:
            raise ValueError("挤出结果为空")
            
    except Exception as e:
        log_message(f"创建挤出实体失败: {str(e)}")
        raise e

def preprocess_mesh_for_tetrahedralization(mesh, mesh_name="网格"):
    """预处理网格以提高四面体化质量"""
    try:
        log_message(f"预处理{mesh_name}...")
        
        processed_mesh = mesh.copy()
        processed_mesh = processed_mesh.clean(tolerance=1e-6)
        
        if not processed_mesh.is_all_triangles:
            processed_mesh = processed_mesh.triangulate()
        
        processed_mesh = processed_mesh.clean(tolerance=1e-8)
        
        log_message(f"  {mesh_name}预处理完成: {processed_mesh.n_points}个点, {processed_mesh.n_cells}个面")
        
        return processed_mesh
        
    except Exception as e:
        log_message(f"预处理{mesh_name}失败: {str(e)}")
        return mesh

def create_high_quality_tetrahedral_mesh(pyvista_mesh, mesh_name="网格"):
    """创建高精度四面体网格"""
    try:
        log_message(f"为{mesh_name}创建高精度四面体网格...")
        
        surface_mesh = pyvista_mesh.extract_surface()
        
        if not surface_mesh.is_all_triangles:
            surface_mesh = surface_mesh.triangulate()
        
        log_message(f"  {mesh_name}表面网格: {surface_mesh.n_points}个点, {surface_mesh.n_cells}个三角形")
        
        # 方法1: 使用保守的细化参数
        try:
            refined_mesh = surface_mesh.subdivide(nsub=1, subfilter='linear')
            log_message(f"  {mesh_name}网格细化: {refined_mesh.n_points}个点, {refined_mesh.n_cells}个面")
            
            tet_mesh = refined_mesh.delaunay_3d(alpha=0, tol=1e-06, offset=1.0)
            
            if tet_mesh.n_cells > 0:
                log_message(f"  {mesh_name}细化四面体网格化成功: {tet_mesh.n_points}个点, {tet_mesh.n_cells}个四面体")
                return tet_mesh
            else:
                raise ValueError("细化四面体化结果为空")
                
        except Exception as e1:
            log_message(f"  {mesh_name}细化方法失败: {str(e1)}")
            
            # 方法2: 使用默认参数
            try:
                tet_mesh = surface_mesh.delaunay_3d()
                
                if tet_mesh.n_cells > 0:
                    log_message(f"  {mesh_name}默认四面体网格化成功: {tet_mesh.n_points}个点, {tet_mesh.n_cells}个四面体")
                    return tet_mesh
                else:
                    raise ValueError("默认四面体化结果为空")
                    
            except Exception as e2:
                log_message(f"  {mesh_name}默认方法也失败: {str(e2)}")
                return None
        
    except Exception as e:
        log_message(f"  {mesh_name}高精度四面体网格化失败: {str(e)}")
        return None

def test_real_ts_extrusion():
    """测试真实TS文件的挤出修复"""
    log_message("=== 测试真实TS文件挤出实体形态保持修复 ===")
    
    # 使用较小的TS面文件进行测试
    ts_surface_file = "dixing2507101.ts"
    
    if not os.path.exists(ts_surface_file):
        log_message(f"TS面文件不存在: {ts_surface_file}")
        return False
    
    # 读取TS面数据
    log_message(f"读取TS面文件: {ts_surface_file}")
    surface_vertices, surface_faces = read_tsurf_data(ts_surface_file)
    
    if len(surface_vertices) == 0:
        log_message("无法读取TS面数据")
        return False
    
    log_message(f"TS面数据: {len(surface_vertices)} 顶点, {len(surface_faces)} 面")
    
    # 限制数据大小以便测试（取前1000个顶点）
    if len(surface_vertices) > 1000:
        log_message("数据量较大，取前1000个顶点进行测试...")
        surface_vertices = surface_vertices[:1000]
        # 过滤面，只保留有效的面
        valid_faces = []
        for face in surface_faces:
            if all(v < 1000 for v in face):
                valid_faces.append(face)
        surface_faces = np.array(valid_faces[:500])  # 限制面数
        log_message(f"限制后数据: {len(surface_vertices)} 顶点, {len(surface_faces)} 面")
    
    try:
        # 创建PyVista表面网格
        faces_with_header = []
        for face in surface_faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        
        surface_mesh = pv.PolyData(surface_vertices, np.array(faces_with_header))
        
        # 计算挤出方向
        surface_mesh = surface_mesh.compute_normals()
        normals = surface_mesh.point_normals
        avg_normal = np.mean(normals, axis=0)
        avg_normal = avg_normal / np.linalg.norm(avg_normal)
        
        thickness = 10.0  # 使用较大的厚度便于观察
        extrusion_vector = avg_normal * thickness
        
        log_message(f"挤出参数: 厚度={thickness}, 方向={avg_normal}")
        
        # 使用修复后的方法创建挤出实体
        extruded_solid = create_high_quality_extruded_solid(surface_mesh, extrusion_vector, thickness)
        
        # 清理和优化
        extruded_solid_clean = extruded_solid.clean().triangulate()
        log_message(f"清理后挤出实体: {extruded_solid_clean.n_points} 顶点, {extruded_solid_clean.n_cells} 面")
        
        # 保存原始挤出实体
        original_obj = "real_ts_extruded_original.obj"
        extruded_solid_clean.save(original_obj)
        log_message(f"✓ 保存原始挤出实体: {original_obj}")
        
        # 预处理挤出实体
        preprocessed_extruded_solid = preprocess_mesh_for_tetrahedralization(extruded_solid_clean, "挤出实体")
        
        # 使用修复后的高精度四面体化
        tet_mesh = create_high_quality_tetrahedral_mesh(preprocessed_extruded_solid, "挤出实体")
        
        if tet_mesh is not None:
            # 从四面体网格提取表面
            surface_from_tet = tet_mesh.extract_surface()
            log_message(f"从四面体提取表面: {surface_from_tet.n_points} 顶点, {surface_from_tet.n_cells} 面")
            
            # 保存四面体化后的表面
            tet_surface_obj = "real_ts_extruded_from_tet.obj"
            surface_from_tet.save(tet_surface_obj)
            log_message(f"✓ 保存四面体化后表面: {tet_surface_obj}")
            
            # 比较形态差异
            original_bounds = extruded_solid_clean.bounds
            tet_bounds = surface_from_tet.bounds
            
            log_message("\n=== 真实TS文件形态比较 ===")
            log_message(f"原始挤出实体边界: {original_bounds}")
            log_message(f"四面体化后边界: {tet_bounds}")
            
            # 计算边界差异
            bounds_diff = np.array(tet_bounds) - np.array(original_bounds)
            max_diff = np.max(np.abs(bounds_diff))
            relative_diff = max_diff / max(np.max(np.abs(original_bounds)), 1e-6) * 100
            
            log_message(f"最大边界差异: {max_diff:.6f}")
            log_message(f"相对边界差异: {relative_diff:.2f}%")
            
            if relative_diff < 5:  # 5%的容差
                log_message("✓ 真实TS文件形态保持良好")
                success = True
            else:
                log_message("✗ 真实TS文件形态发生变化")
                success = False
            
            log_message(f"\n文件已保存:")
            log_message(f"- 原始挤出实体: {original_obj}")
            log_message(f"- 四面体化后表面: {tet_surface_obj}")
            log_message("请在GOCAD或其他软件中查看对比效果")
            
            return success
        else:
            log_message("✗ 高精度四面体化失败")
            return False
        
    except Exception as e:
        log_message(f"✗ 真实TS文件测试失败: {e}")
        return False

def main():
    """主函数"""
    log_message("开始真实TS文件挤出实体形态保持修复测试...")
    
    real_test = test_real_ts_extrusion()
    
    if real_test:
        log_message("\n🎉 真实TS文件挤出实体形态保持修复测试成功！")
        log_message("✓ 真实数据处理正常")
        log_message("✓ 形态保持效果良好")
        log_message("✓ 修复方案在实际应用中有效")
    else:
        log_message("\n❌ 真实TS文件挤出实体形态保持修复测试失败")
    
    return real_test

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        log_message(f"程序异常: {e}")
        sys.exit(1)
