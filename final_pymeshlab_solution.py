#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终PyMeshLab解决方案
基于对算法差异的深入理解，尝试最后的解决方法
"""

import os
import sys
import numpy as np
import re
from datetime import datetime
import traceback

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def read_tsurf_data_ultra_precise(file_path):
    """
    超高精度读取TS文件数据
    """
    log_message(f"超高精度读取TS文件: {os.path.basename(file_path)}")
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                for line in file:
                    line = line.strip()
                    if not line:
                        continue
                    
                    if 'VRTX' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x]
                            if len(nums) >= 4:
                                vertex_id = int(nums[0])
                                # 使用最高精度
                                x = np.longdouble(nums[1])
                                y = np.longdouble(nums[2])
                                z = np.longdouble(nums[3])
                                vrtx.append([x, y, z])
                                vrtx_map[vertex_id] = current_idx
                                current_idx += 1
                        except:
                            continue
                    
                    elif 'TRGL' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x and x.isdigit()]
                            if len(nums) == 3:
                                v1 = vrtx_map[int(nums[0])]
                                v2 = vrtx_map[int(nums[1])]
                                v3 = vrtx_map[int(nums[2])]
                                trgl.append([v1, v2, v3])
                        except:
                            continue
            
            if len(vrtx) > 0 and len(trgl) > 0:
                log_message(f"  成功: {len(vrtx)} 顶点, {len(trgl)} 面")
                return np.array(vrtx, dtype=np.longdouble), np.array(trgl)
                
        except:
            continue
    
    return np.array([]), np.array([])

def ultra_precise_coordinate_normalization(vertices1, vertices2):
    """
    超精确坐标标准化
    """
    log_message("执行超精确坐标标准化...")
    
    all_vertices = np.vstack([vertices1, vertices2])
    
    # 使用质心作为参考点而不是最小值
    centroid = np.mean(all_vertices, axis=0)
    
    vertices1_norm = vertices1 - centroid
    vertices2_norm = vertices2 - centroid
    
    log_message(f"质心偏移: X={centroid[0]:.15f}, Y={centroid[1]:.15f}, Z={centroid[2]:.15f}")
    
    # 进一步缩放到合理范围
    max_coord = np.max(np.abs(np.vstack([vertices1_norm, vertices2_norm])))
    if max_coord > 1000:
        scale_factor = 1000.0 / max_coord
        vertices1_norm *= scale_factor
        vertices2_norm *= scale_factor
        log_message(f"应用缩放因子: {scale_factor:.15f}")
    else:
        scale_factor = 1.0
    
    return vertices1_norm, vertices2_norm, centroid, scale_factor

def write_ultra_precise_obj(file_path, vertices, faces):
    """
    写入超高精度OBJ文件
    """
    try:
        with open(file_path, 'w') as f:
            # 写入顶点，使用最高精度
            for v in vertices:
                f.write(f"v {v[0]:.15f} {v[1]:.15f} {v[2]:.15f}\n")
            
            # 写入面
            for face in faces:
                f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
        
        return True
    except Exception as e:
        log_message(f"写入OBJ文件失败: {e}")
        return False

def ultra_mesh_repair(ms, mesh_name):
    """
    超级网格修复，尝试所有可能的修复方法
    """
    try:
        log_message(f"超级修复: {mesh_name}")
        
        original_vertices = len(ms.current_mesh().vertex_matrix())
        original_faces = len(ms.current_mesh().face_matrix())
        log_message(f"  原始: {original_vertices} 顶点, {original_faces} 面")
        
        # 步骤1: 极致清理
        log_message("  步骤1: 极致清理...")
        ms.meshing_remove_duplicate_vertices()
        ms.meshing_remove_duplicate_faces()
        ms.meshing_remove_null_faces()
        ms.meshing_remove_unreferenced_vertices()
        
        # 步骤2: 合并非常接近的顶点
        log_message("  步骤2: 合并接近顶点...")
        try:
            ms.meshing_merge_close_vertices(threshold=1e-10)
        except:
            pass
        
        # 步骤3: 修复非流形
        log_message("  步骤3: 修复非流形...")
        try:
            ms.meshing_repair_non_manifold_edges()
            ms.meshing_repair_non_manifold_vertices()
        except Exception as e:
            log_message(f"    非流形修复警告: {e}")
        
        # 步骤4: 尝试最小的孔洞闭合
        log_message("  步骤4: 最小孔洞闭合...")
        try:
            # 只闭合非常小的孔洞
            ms.meshing_close_holes(maxholesize=3, selfintersection=False)
        except Exception as e:
            log_message(f"    孔洞闭合警告: {e}")
        
        # 步骤5: 重新定向面法向
        log_message("  步骤5: 统一面法向...")
        try:
            ms.meshing_re_orient_faces_coherently()
        except Exception as e:
            log_message(f"    法向统一警告: {e}")
        
        # 步骤6: 最终清理
        log_message("  步骤6: 最终清理...")
        ms.meshing_remove_duplicate_vertices()
        ms.meshing_remove_unreferenced_vertices()
        
        final_vertices = len(ms.current_mesh().vertex_matrix())
        final_faces = len(ms.current_mesh().face_matrix())
        log_message(f"  最终: {final_vertices} 顶点, {final_faces} 面")
        
        return True
        
    except Exception as e:
        log_message(f"超级修复失败: {e}")
        return False

def try_pymeshlab_with_all_tricks(file1, file2, output_dir, centroid, scale_factor):
    """
    使用所有技巧尝试PyMeshLab布尔运算
    """
    try:
        import pymeshlab
        
        log_message("=== PyMeshLab终极尝试 ===")
        
        results = {}
        operations = [
            ("union", "并集"),  # 先尝试最容易的
            ("intersection", "交集"),
            ("difference", "差集")
        ]
        
        for op_type, op_name in operations:
            try:
                log_message(f"\n执行{op_name}运算...")
                
                # 创建新的MeshSet
                ms = pymeshlab.MeshSet()
                ms.load_new_mesh(file1)
                ms.load_new_mesh(file2)
                
                # 超级修复两个网格
                log_message("  超级修复网格1...")
                ms.set_current_mesh(0)
                ultra_mesh_repair(ms, "网格1")
                
                log_message("  超级修复网格2...")
                ms.set_current_mesh(1)
                ultra_mesh_repair(ms, "网格2")
                
                # 尝试布尔运算
                log_message(f"  执行{op_name}...")
                ms.set_current_mesh(0)
                
                if op_type == "intersection":
                    ms.generate_boolean_intersection(first_mesh=0, second_mesh=1)
                elif op_type == "difference":
                    ms.generate_boolean_difference(first_mesh=0, second_mesh=1)
                elif op_type == "union":
                    ms.generate_boolean_union(first_mesh=0, second_mesh=1)
                
                # 删除第二个网格
                ms.set_current_mesh(1)
                ms.delete_current_mesh()
                
                # 检查结果
                result_mesh = ms.current_mesh()
                vertex_count = len(result_mesh.vertex_matrix())
                face_count = len(result_mesh.face_matrix())
                
                if vertex_count > 0 and face_count > 0:
                    log_message(f"  ✓ {op_name}成功: {vertex_count} 顶点, {face_count} 面")
                    
                    # 保存结果
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    obj_file = os.path.join(output_dir, f"final_{op_type}_{timestamp}.obj")
                    ts_file = os.path.join(output_dir, f"final_{op_type}_{timestamp}.ts")
                    
                    # 保存OBJ格式
                    ms.save_current_mesh(obj_file)
                    
                    # 读取结果并还原坐标
                    vertices, faces = read_obj_file(obj_file)
                    if len(vertices) > 0:
                        # 还原缩放和平移
                        vertices_restored = (vertices / scale_factor) + centroid
                        write_ts_file(ts_file, vertices_restored, faces, f"final_{op_type}_result")
                        log_message(f"  已保存: {os.path.basename(ts_file)}")
                        
                        results[op_type] = {
                            'success': True,
                            'obj_file': obj_file,
                            'ts_file': ts_file,
                            'vertices': vertex_count,
                            'faces': face_count
                        }
                else:
                    log_message(f"  ✗ {op_name}结果为空")
                    results[op_type] = {'success': False, 'reason': '结果为空'}
                    
            except Exception as e:
                log_message(f"  ✗ {op_name}失败: {e}")
                results[op_type] = {'success': False, 'reason': str(e)}
        
        return results
        
    except Exception as e:
        log_message(f"PyMeshLab终极尝试整体失败: {e}")
        return {}

def read_obj_file(file_path):
    """读取OBJ文件"""
    vertices = []
    faces = []
    
    try:
        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('v '):
                    parts = line.split()
                    if len(parts) >= 4:
                        vertices.append([float(parts[1]), float(parts[2]), float(parts[3])])
                elif line.startswith('f '):
                    parts = line.split()
                    if len(parts) >= 4:
                        face = []
                        for i in range(1, 4):
                            vertex_idx = int(parts[i].split('/')[0]) - 1
                            face.append(vertex_idx)
                        faces.append(face)
        
        return np.array(vertices), np.array(faces)
    except Exception as e:
        log_message(f"读取OBJ文件失败: {e}")
        return np.array([]), np.array([])

def write_ts_file(file_path, vertices, faces, name="result"):
    """写入TS文件"""
    try:
        with open(file_path, 'w') as f:
            f.write(f"GOCAD TSurf 1\n")
            f.write(f"HEADER {{\n")
            f.write(f"name: {name}\n")
            f.write(f"}}\n")
            f.write("TFACE\n")
            
            for i, v in enumerate(vertices):
                f.write(f"VRTX {i+1} {v[0]:.15f} {v[1]:.15f} {v[2]:.15f}\n")
            
            for face in faces:
                f.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            f.write("END\n")
        
        return True
    except Exception as e:
        log_message(f"写入TS文件失败: {e}")
        return False

def main():
    """主函数"""
    log_message("=== PyMeshLab最终解决方案 ===")
    log_message("基于算法差异分析的终极尝试")
    
    # 检查依赖
    try:
        import pymeshlab
        log_message("PyMeshLab已安装")
    except ImportError:
        log_message("错误: PyMeshLab未安装")
        return False
    
    # 检查输入文件
    file1 = "1_05-J2a_1.ts"
    file2 = "dixing2507101.ts"
    
    if not os.path.exists(file1) or not os.path.exists(file2):
        log_message("错误: 找不到输入文件")
        return False
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"final_results_{timestamp}"
    temp_dir = f"temp_final_{timestamp}"
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(temp_dir, exist_ok=True)
        log_message(f"输出目录: {output_dir}")
        
        # 步骤1: 超高精度读取
        log_message("\n=== 步骤1: 超高精度读取 ===")
        vertices1, faces1 = read_tsurf_data_ultra_precise(file1)
        vertices2, faces2 = read_tsurf_data_ultra_precise(file2)
        
        if len(vertices1) == 0 or len(vertices2) == 0:
            log_message("错误: 无法读取输入文件")
            return False
        
        # 步骤2: 超精确坐标标准化
        log_message("\n=== 步骤2: 超精确坐标标准化 ===")
        vertices1_norm, vertices2_norm, centroid, scale_factor = ultra_precise_coordinate_normalization(vertices1, vertices2)
        
        # 步骤3: 创建超精确OBJ文件
        log_message("\n=== 步骤3: 创建超精确OBJ文件 ===")
        temp_obj1 = os.path.join(temp_dir, "mesh1_ultra.obj")
        temp_obj2 = os.path.join(temp_dir, "mesh2_ultra.obj")
        
        write_ultra_precise_obj(temp_obj1, vertices1_norm, faces1)
        write_ultra_precise_obj(temp_obj2, vertices2_norm, faces2)
        
        # 步骤4: PyMeshLab终极尝试
        log_message("\n=== 步骤4: PyMeshLab终极尝试 ===")
        results = try_pymeshlab_with_all_tricks(temp_obj1, temp_obj2, output_dir, centroid, scale_factor)
        
        # 步骤5: 结果总结
        log_message("\n=== 最终结果总结 ===")
        success_count = 0
        
        for op_type in ["union", "intersection", "difference"]:
            if op_type in results and results[op_type]['success']:
                result = results[op_type]
                log_message(f"✓ {op_type}: 成功 (顶点={result['vertices']}, 面={result['faces']})")
                success_count += 1
            else:
                reason = results.get(op_type, {}).get('reason', '未执行')
                log_message(f"✗ {op_type}: 失败 - {reason}")
        
        # 清理临时文件
        try:
            import shutil
            shutil.rmtree(temp_dir)
        except:
            pass
        
        log_message(f"\n处理完成！成功: {success_count}/3")
        log_message(f"结果保存在: {output_dir}")
        
        if success_count > 0:
            log_message("\n🎉 PyMeshLab终极方案成功！")
            log_message("证明了通过精确的预处理可以让PyMeshLab处理这些文件。")
        else:
            log_message("\n❌ 所有方法都失败了。")
            log_message("这些文件可能确实无法满足PyMeshLab的严格要求。")
        
        return success_count > 0
        
    except Exception as e:
        log_message(f"程序执行失败: {e}")
        log_message(traceback.format_exc())
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log_message("程序被用户中断")
        sys.exit(1)
    except Exception as e:
        log_message(f"程序异常退出: {e}")
        sys.exit(1)
