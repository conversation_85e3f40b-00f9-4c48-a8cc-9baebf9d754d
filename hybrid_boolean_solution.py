#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合布尔运算解决方案
使用PyVista进行布尔运算，然后使用PyMeshLab进行后处理和闭合
这样结合了两个库的优势：PyVista的宽容性和PyMeshLab的闭合能力
"""

import os
import sys
import numpy as np
import re
from datetime import datetime
import traceback

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def read_tsurf_data(file_path):
    """读取TS文件数据"""
    log_message(f"读取TS文件: {os.path.basename(file_path)}")
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                for line in file:
                    line = line.strip()
                    if not line:
                        continue
                    
                    if 'VRTX' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x]
                            if len(nums) >= 4:
                                vertex_id = int(nums[0])
                                x, y, z = float(nums[1]), float(nums[2]), float(nums[3])
                                vrtx.append([x, y, z])
                                vrtx_map[vertex_id] = current_idx
                                current_idx += 1
                        except:
                            continue
                    
                    elif 'TRGL' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x and x.isdigit()]
                            if len(nums) == 3:
                                v1 = vrtx_map[int(nums[0])]
                                v2 = vrtx_map[int(nums[1])]
                                v3 = vrtx_map[int(nums[2])]
                                trgl.append([v1, v2, v3])
                        except:
                            continue
            
            if len(vrtx) > 0 and len(trgl) > 0:
                log_message(f"  成功: {len(vrtx)} 顶点, {len(trgl)} 面")
                return np.array(vrtx), np.array(trgl)
                
        except:
            continue
    
    return np.array([]), np.array([])

def normalize_coordinates(vertices1, vertices2):
    """坐标标准化"""
    all_vertices = np.vstack([vertices1, vertices2])
    min_coords = np.min(all_vertices, axis=0)
    
    vertices1_norm = vertices1 - min_coords
    vertices2_norm = vertices2 - min_coords
    
    log_message(f"坐标偏移: X={min_coords[0]:.1f}, Y={min_coords[1]:.1f}, Z={min_coords[2]:.1f}")
    return vertices1_norm, vertices2_norm, min_coords

def write_ts_file(file_path, vertices, faces, name="result"):
    """写入TS文件"""
    try:
        with open(file_path, 'w') as f:
            f.write(f"GOCAD TSURF 1\nHEADER {{\nname: {name}\n}}\nTFACE\n")
            for i, v in enumerate(vertices):
                f.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            for face in faces:
                f.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            f.write("END\n")
        return True
    except Exception as e:
        log_message(f"写入TS文件失败: {e}")
        return False

def create_pyvista_mesh(vertices, faces):
    """创建PyVista网格"""
    try:
        import pyvista as pv
        
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        
        mesh = pv.PolyData(vertices, np.array(faces_with_header))
        return mesh
    except Exception as e:
        log_message(f"创建PyVista网格失败: {e}")
        return None

def pyvista_boolean_operations(vertices1, faces1, vertices2, faces2, temp_dir):
    """
    使用PyVista执行布尔运算
    返回结果网格的临时文件路径
    """
    try:
        import pyvista as pv
        
        log_message("=== PyVista布尔运算阶段 ===")
        
        # 创建PyVista网格
        mesh1 = create_pyvista_mesh(vertices1, faces1)
        mesh2 = create_pyvista_mesh(vertices2, faces2)
        
        if mesh1 is None or mesh2 is None:
            log_message("无法创建PyVista网格")
            return {}
        
        log_message(f"网格1: {mesh1.n_points} 顶点, {mesh1.n_cells} 面")
        log_message(f"网格2: {mesh2.n_points} 顶点, {mesh2.n_cells} 面")
        
        results = {}
        operations = [
            ("intersection", "交集", "boolean_intersection"),
            ("difference", "差集", "boolean_difference"),
            ("union", "并集", "boolean_union")
        ]
        
        for op_type, op_name, method_name in operations:
            try:
                log_message(f"执行{op_name}...")
                
                # 执行布尔运算
                method = getattr(mesh1, method_name)
                result_mesh = method(mesh2)
                
                if result_mesh.n_points > 0 and result_mesh.n_cells > 0:
                    log_message(f"  ✓ {op_name}成功: {result_mesh.n_points} 顶点, {result_mesh.n_cells} 面")
                    
                    # 保存临时OBJ文件
                    temp_obj = os.path.join(temp_dir, f"pyvista_{op_type}.obj")
                    result_mesh.save(temp_obj)
                    
                    results[op_type] = {
                        'success': True,
                        'temp_obj': temp_obj,
                        'vertices': result_mesh.n_points,
                        'faces': result_mesh.n_cells
                    }
                else:
                    log_message(f"  ✗ {op_name}结果为空")
                    results[op_type] = {'success': False, 'reason': '结果为空'}
                    
            except Exception as e:
                log_message(f"  ✗ {op_name}失败: {e}")
                results[op_type] = {'success': False, 'reason': str(e)}
        
        return results
        
    except ImportError:
        log_message("错误: PyVista未安装")
        return {}
    except Exception as e:
        log_message(f"PyVista布尔运算失败: {e}")
        return {}

def pymeshlab_post_process(temp_obj_file, output_dir, coord_offset, op_type):
    """
    使用PyMeshLab对PyVista的结果进行后处理和闭合
    """
    try:
        import pymeshlab
        
        log_message(f"PyMeshLab后处理: {op_type}")
        
        ms = pymeshlab.MeshSet()
        ms.load_new_mesh(temp_obj_file)
        
        original_vertices = len(ms.current_mesh().vertex_matrix())
        original_faces = len(ms.current_mesh().face_matrix())
        log_message(f"  输入: {original_vertices} 顶点, {original_faces} 面")
        
        # 步骤1: 基础清理
        log_message("  步骤1: 基础清理...")
        ms.meshing_remove_duplicate_vertices()
        ms.meshing_remove_duplicate_faces()
        ms.meshing_remove_null_faces()
        ms.meshing_remove_unreferenced_vertices()
        
        # 步骤2: 修复非流形
        log_message("  步骤2: 修复非流形...")
        try:
            ms.meshing_repair_non_manifold_edges()
            ms.meshing_repair_non_manifold_vertices()
        except Exception as e:
            log_message(f"    非流形修复警告: {e}")
        
        # 步骤3: 闭合孔洞（关键步骤）
        log_message("  步骤3: 闭合孔洞...")
        try:
            # 渐进式闭合孔洞
            ms.meshing_close_holes(maxholesize=50, selfintersection=False)
            log_message("    已闭合小孔洞")
            
            ms.meshing_close_holes(maxholesize=200, selfintersection=False)
            log_message("    已闭合中等孔洞")
            
            ms.meshing_close_holes(maxholesize=1000, selfintersection=False)
            log_message("    已闭合大孔洞")
            
            # 如果仍有孔洞，尝试更大的参数
            ms.meshing_close_holes(maxholesize=5000, selfintersection=False)
            log_message("    已闭合超大孔洞")
            
        except Exception as e:
            log_message(f"    孔洞闭合警告: {e}")
        
        # 步骤4: 重新定向面法向
        log_message("  步骤4: 统一面法向...")
        try:
            ms.meshing_re_orient_faces_coherently()
        except Exception as e:
            log_message(f"    法向统一警告: {e}")
        
        # 步骤5: 最终清理
        log_message("  步骤5: 最终清理...")
        ms.meshing_remove_duplicate_vertices()
        ms.meshing_remove_duplicate_faces()
        ms.meshing_remove_unreferenced_vertices()
        
        final_vertices = len(ms.current_mesh().vertex_matrix())
        final_faces = len(ms.current_mesh().face_matrix())
        log_message(f"  输出: {final_vertices} 顶点, {final_faces} 面")
        
        # 保存最终结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        obj_file = os.path.join(output_dir, f"hybrid_{op_type}_{timestamp}.obj")
        ts_file = os.path.join(output_dir, f"hybrid_{op_type}_{timestamp}.ts")
        
        # 保存OBJ格式
        ms.save_current_mesh(obj_file)
        
        # 转换为TS格式
        vertices, faces = read_obj_file(obj_file)
        if len(vertices) > 0:
            vertices_restored = vertices + coord_offset
            write_ts_file(ts_file, vertices_restored, faces, f"hybrid_{op_type}_result")
            log_message(f"  已保存: {os.path.basename(ts_file)}")
            
            return {
                'success': True,
                'obj_file': obj_file,
                'ts_file': ts_file,
                'vertices': final_vertices,
                'faces': final_faces
            }
        else:
            return {'success': False, 'reason': '无法读取结果'}
        
    except ImportError:
        log_message("错误: PyMeshLab未安装")
        return {'success': False, 'reason': 'PyMeshLab未安装'}
    except Exception as e:
        log_message(f"PyMeshLab后处理失败: {e}")
        return {'success': False, 'reason': str(e)}

def read_obj_file(file_path):
    """读取OBJ文件"""
    vertices = []
    faces = []
    
    try:
        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('v '):
                    parts = line.split()
                    if len(parts) >= 4:
                        vertices.append([float(parts[1]), float(parts[2]), float(parts[3])])
                elif line.startswith('f '):
                    parts = line.split()
                    if len(parts) >= 4:
                        face = []
                        for i in range(1, 4):
                            vertex_idx = int(parts[i].split('/')[0]) - 1
                            face.append(vertex_idx)
                        faces.append(face)
        
        return np.array(vertices), np.array(faces)
    except Exception as e:
        log_message(f"读取OBJ文件失败: {e}")
        return np.array([]), np.array([])

def main():
    """主函数"""
    log_message("=== 混合布尔运算解决方案 ===")
    log_message("PyVista布尔运算 + PyMeshLab后处理闭合")

    # 检查依赖
    try:
        import pyvista as pv
        import pymeshlab
        log_message(f"PyVista版本: {pv.__version__}")
        log_message("PyMeshLab已安装")
    except ImportError as e:
        log_message(f"错误: 缺少依赖 - {e}")
        return False

    # 检查输入文件
    file1 = "1_05-J2a_1.ts"
    file2 = "dixing2507101.ts"

    if not os.path.exists(file1) or not os.path.exists(file2):
        log_message("错误: 找不到输入文件")
        return False

    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"hybrid_results_{timestamp}"
    temp_dir = f"temp_hybrid_{timestamp}"

    try:
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(temp_dir, exist_ok=True)
        log_message(f"输出目录: {output_dir}")

        # 步骤1: 读取TS文件
        log_message("\n=== 步骤1: 读取文件 ===")
        vertices1, faces1 = read_tsurf_data(file1)
        vertices2, faces2 = read_tsurf_data(file2)

        if len(vertices1) == 0 or len(vertices2) == 0:
            log_message("错误: 无法读取输入文件")
            return False

        # 步骤2: 坐标标准化
        log_message("\n=== 步骤2: 坐标标准化 ===")
        vertices1_norm, vertices2_norm, coord_offset = normalize_coordinates(vertices1, vertices2)

        # 步骤3: PyVista布尔运算
        log_message("\n=== 步骤3: PyVista布尔运算 ===")
        pyvista_results = pyvista_boolean_operations(vertices1_norm, faces1, vertices2_norm, faces2, temp_dir)

        # 步骤4: PyMeshLab后处理
        log_message("\n=== 步骤4: PyMeshLab后处理和闭合 ===")
        final_results = {}

        for op_type in ["intersection", "difference", "union"]:
            if op_type in pyvista_results and pyvista_results[op_type]['success']:
                log_message(f"\n处理{op_type}结果...")
                temp_obj = pyvista_results[op_type]['temp_obj']

                post_result = pymeshlab_post_process(temp_obj, output_dir, coord_offset, op_type)
                final_results[op_type] = post_result
            else:
                log_message(f"\n跳过{op_type}（PyVista阶段失败）")
                final_results[op_type] = {'success': False, 'reason': 'PyVista阶段失败'}

        # 步骤5: 结果总结
        log_message("\n=== 最终结果总结 ===")
        success_count = 0

        for op_type in ["intersection", "difference", "union"]:
            if op_type in final_results and final_results[op_type]['success']:
                result = final_results[op_type]
                log_message(f"✓ {op_type}: 成功 (顶点={result['vertices']}, 面={result['faces']})")
                success_count += 1
            else:
                reason = final_results.get(op_type, {}).get('reason', '未执行')
                log_message(f"✗ {op_type}: 失败 - {reason}")

        # 清理临时文件
        try:
            import shutil
            shutil.rmtree(temp_dir)
        except:
            pass

        log_message(f"\n处理完成！成功: {success_count}/3")
        log_message(f"结果保存在: {output_dir}")

        if success_count > 0:
            log_message("\n🎉 混合方法成功！")
            log_message("结果应该是闭合的网格，结合了PyVista的布尔运算能力和PyMeshLab的闭合能力。")
        else:
            log_message("\n❌ 混合方法也失败了。")

        return success_count > 0

    except Exception as e:
        log_message(f"程序执行失败: {e}")
        log_message(traceback.format_exc())
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log_message("程序被用户中断")
        sys.exit(1)
    except Exception as e:
        log_message(f"程序异常退出: {e}")
        sys.exit(1)
