#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试"执行模型与面布尔运算(不闭合)"模块是否已成功删除
"""

import os
import sys

def log_message(message):
    """打印带时间戳的日志消息"""
    from datetime import datetime
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_module_removal():
    """测试模块删除"""
    log_message("=== 测试'执行模型与面布尔运算(不闭合)'模块删除 ===")
    
    # 检查文件是否存在
    ui_file = "ui_integrated_w5.py"
    if not os.path.exists(ui_file):
        log_message("✗ UI文件不存在")
        return False
    
    log_message("✓ UI文件存在")
    
    # 检查是否还有相关代码
    with open(ui_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查按钮定义
    if "btn_boolean_ts_surface_pyvista" in content:
        log_message("✗ 仍然存在按钮定义")
        return False
    else:
        log_message("✓ 按钮定义已删除")
    
    # 检查函数定义
    if "perform_ts_surface_boolean_operations_pyvista" in content:
        log_message("✗ 仍然存在函数定义")
        return False
    else:
        log_message("✓ 函数定义已删除")
    
    # 检查"不闭合"相关文本
    if "不闭合" in content:
        log_message("⚠️  仍然存在'不闭合'相关文本")
        # 计算出现次数
        count = content.count("不闭合")
        log_message(f"   出现次数: {count}")
        if count > 0:
            log_message("   这可能是注释或其他地方的引用")
    else:
        log_message("✓ '不闭合'相关文本已清理")
    
    # 检查PyVista相关的布尔运算
    pyvista_boolean_count = content.count("pyvista_")
    if pyvista_boolean_count > 0:
        log_message(f"⚠️  仍然存在 {pyvista_boolean_count} 个PyVista相关引用")
        log_message("   这可能是其他功能的正常使用")
    else:
        log_message("✓ PyVista布尔运算相关代码已清理")
    
    return True

def test_remaining_functionality():
    """测试剩余功能是否完整"""
    log_message("\n=== 测试剩余功能完整性 ===")
    
    ui_file = "ui_integrated_w5.py"
    with open(ui_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查主要功能是否还在
    main_functions = [
        "perform_ts_boolean_operations",  # TS模型间布尔运算
        "perform_ts_surface_boolean_operations",  # TS模型与面布尔运算（四面体网格化版本）
        "create_tetrahedral_mesh",  # 四面体网格化
        "perform_tetrahedral_boolean_operations"  # 四面体布尔运算
    ]
    
    for func in main_functions:
        if func in content:
            log_message(f"✓ {func} 功能保留")
        else:
            log_message(f"✗ {func} 功能缺失")
            return False
    
    # 检查按钮是否还在
    if "btn_boolean_ts_surface" in content and "btn_boolean_ts_surface_pyvista" not in content:
        log_message("✓ 主要布尔运算按钮保留，不闭合按钮已删除")
    else:
        log_message("✗ 按钮状态异常")
        return False
    
    return True

def main():
    """主函数"""
    log_message("开始测试模块删除结果...")
    
    removal_success = test_module_removal()
    functionality_intact = test_remaining_functionality()
    
    if removal_success and functionality_intact:
        log_message("\n🎉 模块删除成功！")
        log_message("✓ '执行模型与面布尔运算(不闭合)'模块已完全删除")
        log_message("✓ 主要功能保持完整")
        log_message("✓ 现在只保留四面体网格化的布尔运算方案")
        log_message("\n当前可用功能:")
        log_message("1. TS模型间布尔运算 - 四面体网格化方案")
        log_message("2. TS模型与面布尔运算 - 四面体网格化方案")
        log_message("3. 所有结果都是完全闭合的网格")
    else:
        log_message("\n❌ 模块删除可能不完整")
        if not removal_success:
            log_message("- 删除过程中发现残留代码")
        if not functionality_intact:
            log_message("- 主要功能可能受到影响")
    
    return removal_success and functionality_intact

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        log_message(f"程序异常: {e}")
        sys.exit(1)
