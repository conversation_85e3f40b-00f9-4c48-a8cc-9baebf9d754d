#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查网格闭合性的脚本
验证PyVista布尔运算结果是否为闭合网格
"""

import os
import sys
import numpy as np
from datetime import datetime

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_mesh_closure_pyvista(mesh_file):
    """使用PyVista检查网格闭合性"""
    try:
        import pyvista as pv
        
        log_message(f"使用PyVista检查: {os.path.basename(mesh_file)}")
        
        # 加载网格
        if mesh_file.endswith('.obj'):
            mesh = pv.read(mesh_file)
        else:
            log_message("  错误: 只支持OBJ文件")
            return None
        
        log_message(f"  网格信息: {mesh.n_points} 顶点, {mesh.n_cells} 面")
        
        # 检查基本属性
        results = {
            'file': os.path.basename(mesh_file),
            'vertices': mesh.n_points,
            'faces': mesh.n_cells,
            'bounds': mesh.bounds
        }
        
        # 检查是否为流形
        try:
            # 提取表面（如果是体网格）
            if hasattr(mesh, 'extract_surface'):
                surface = mesh.extract_surface()
            else:
                surface = mesh
            
            # 检查边界
            edges = surface.extract_feature_edges(boundary_edges=True, 
                                                 non_manifold_edges=False, 
                                                 manifold_edges=False)
            
            boundary_edge_count = edges.n_cells if edges.n_cells > 0 else 0
            results['boundary_edges'] = boundary_edge_count
            results['is_closed'] = boundary_edge_count == 0
            
            log_message(f"  边界边数: {boundary_edge_count}")
            log_message(f"  是否闭合: {'是' if results['is_closed'] else '否'}")
            
            # 检查非流形边
            non_manifold_edges = surface.extract_feature_edges(boundary_edges=False,
                                                              non_manifold_edges=True,
                                                              manifold_edges=False)
            
            non_manifold_count = non_manifold_edges.n_cells if non_manifold_edges.n_cells > 0 else 0
            results['non_manifold_edges'] = non_manifold_count
            log_message(f"  非流形边数: {non_manifold_count}")
            
            # 计算体积（如果闭合）
            if results['is_closed']:
                try:
                    volume = surface.volume
                    results['volume'] = volume
                    log_message(f"  体积: {volume:.6f}")
                except:
                    results['volume'] = None
                    log_message(f"  体积: 无法计算")
            else:
                results['volume'] = None
            
        except Exception as e:
            log_message(f"  流形检查失败: {e}")
            results['is_closed'] = None
            results['boundary_edges'] = None
            results['non_manifold_edges'] = None
            results['volume'] = None
        
        return results
        
    except ImportError:
        log_message("错误: PyVista未安装")
        return None
    except Exception as e:
        log_message(f"检查失败: {e}")
        return None

def check_mesh_closure_pymeshlab(mesh_file):
    """使用PyMeshLab检查网格闭合性"""
    try:
        import pymeshlab
        
        log_message(f"使用PyMeshLab检查: {os.path.basename(mesh_file)}")
        
        ms = pymeshlab.MeshSet()
        ms.load_new_mesh(mesh_file)
        
        mesh = ms.current_mesh()
        
        results = {
            'file': os.path.basename(mesh_file),
            'vertices': len(mesh.vertex_matrix()),
            'faces': len(mesh.face_matrix()),
        }
        
        log_message(f"  网格信息: {results['vertices']} 顶点, {results['faces']} 面")
        
        # 检查网格属性
        try:
            # 获取网格统计信息
            bbox = mesh.bounding_box()
            results['bounding_box'] = bbox
            
            # 检查是否为流形
            # PyMeshLab没有直接的闭合检查，但可以通过其他方式
            try:
                # 尝试计算体积，如果成功且为正值，通常表示闭合
                # 注意：这不是绝对可靠的方法
                ms.compute_scalar_by_function_per_vertex(function="x")  # 测试操作
                log_message("  网格结构: 正常")
                results['structure_ok'] = True
            except Exception as e:
                log_message(f"  网格结构: 可能有问题 - {e}")
                results['structure_ok'] = False
            
        except Exception as e:
            log_message(f"  属性检查失败: {e}")
            results['structure_ok'] = None
        
        return results
        
    except ImportError:
        log_message("警告: PyMeshLab未安装，跳过PyMeshLab检查")
        return None
    except Exception as e:
        log_message(f"PyMeshLab检查失败: {e}")
        return None

def check_mesh_closure_manual(mesh_file):
    """手动检查网格闭合性（基于边的分析）"""
    try:
        log_message(f"手动检查: {os.path.basename(mesh_file)}")
        
        vertices = []
        faces = []
        
        # 读取OBJ文件
        with open(mesh_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('v '):
                    parts = line.split()
                    if len(parts) >= 4:
                        vertices.append([float(parts[1]), float(parts[2]), float(parts[3])])
                elif line.startswith('f '):
                    parts = line.split()
                    if len(parts) >= 4:
                        face = []
                        for i in range(1, 4):
                            vertex_idx = int(parts[i].split('/')[0]) - 1
                            face.append(vertex_idx)
                        faces.append(face)
        
        vertices = np.array(vertices)
        faces = np.array(faces)
        
        log_message(f"  读取: {len(vertices)} 顶点, {len(faces)} 面")
        
        # 构建边字典，统计每条边被多少个面共享
        edge_count = {}
        
        for face in faces:
            # 每个三角面有3条边
            edges = [
                tuple(sorted([face[0], face[1]])),
                tuple(sorted([face[1], face[2]])),
                tuple(sorted([face[2], face[0]]))
            ]
            
            for edge in edges:
                if edge in edge_count:
                    edge_count[edge] += 1
                else:
                    edge_count[edge] = 1
        
        # 分析边的共享情况
        boundary_edges = []  # 只被一个面使用的边（边界边）
        manifold_edges = []  # 被两个面共享的边（流形边）
        non_manifold_edges = []  # 被超过两个面共享的边（非流形边）
        
        for edge, count in edge_count.items():
            if count == 1:
                boundary_edges.append(edge)
            elif count == 2:
                manifold_edges.append(edge)
            else:
                non_manifold_edges.append(edge)
        
        total_edges = len(edge_count)
        boundary_count = len(boundary_edges)
        manifold_count = len(manifold_edges)
        non_manifold_count = len(non_manifold_edges)
        
        log_message(f"  总边数: {total_edges}")
        log_message(f"  边界边: {boundary_count}")
        log_message(f"  流形边: {manifold_count}")
        log_message(f"  非流形边: {non_manifold_count}")
        
        # 判断是否闭合
        is_closed = boundary_count == 0 and non_manifold_count == 0
        log_message(f"  是否闭合: {'是' if is_closed else '否'}")
        
        results = {
            'file': os.path.basename(mesh_file),
            'vertices': len(vertices),
            'faces': len(faces),
            'total_edges': total_edges,
            'boundary_edges': boundary_count,
            'manifold_edges': manifold_count,
            'non_manifold_edges': non_manifold_count,
            'is_closed': is_closed
        }
        
        return results
        
    except Exception as e:
        log_message(f"手动检查失败: {e}")
        return None

def main():
    """主函数"""
    log_message("=== 网格闭合性检查 ===")
    
    # 查找PyVista结果目录
    result_dirs = [d for d in os.listdir('.') if d.startswith('pyvista_results_')]
    
    if not result_dirs:
        log_message("错误: 找不到PyVista结果目录")
        return False
    
    # 使用最新的结果目录
    result_dir = sorted(result_dirs)[-1]
    log_message(f"检查目录: {result_dir}")
    
    # 查找OBJ文件
    obj_files = []
    for file in os.listdir(result_dir):
        if file.endswith('.obj'):
            obj_files.append(os.path.join(result_dir, file))
    
    if not obj_files:
        log_message("错误: 找不到OBJ文件")
        return False
    
    log_message(f"找到 {len(obj_files)} 个OBJ文件")
    
    all_results = []
    
    for obj_file in sorted(obj_files):
        log_message(f"\n{'='*50}")
        log_message(f"检查文件: {os.path.basename(obj_file)}")
        log_message(f"{'='*50}")
        
        # 使用多种方法检查
        pyvista_result = check_mesh_closure_pyvista(obj_file)
        pymeshlab_result = check_mesh_closure_pymeshlab(obj_file)
        manual_result = check_mesh_closure_manual(obj_file)
        
        result_summary = {
            'file': os.path.basename(obj_file),
            'pyvista': pyvista_result,
            'pymeshlab': pymeshlab_result,
            'manual': manual_result
        }
        
        all_results.append(result_summary)
    
    # 总结报告
    log_message(f"\n{'='*60}")
    log_message("=== 闭合性检查总结 ===")
    log_message(f"{'='*60}")
    
    for result in all_results:
        log_message(f"\n文件: {result['file']}")
        
        # PyVista结果
        if result['pyvista']:
            pv_result = result['pyvista']
            log_message(f"  PyVista检查:")
            log_message(f"    闭合状态: {'闭合' if pv_result.get('is_closed') else '不闭合'}")
            log_message(f"    边界边数: {pv_result.get('boundary_edges', 'N/A')}")
            log_message(f"    体积: {pv_result.get('volume', 'N/A')}")
        
        # 手动检查结果
        if result['manual']:
            manual_result = result['manual']
            log_message(f"  手动检查:")
            log_message(f"    闭合状态: {'闭合' if manual_result.get('is_closed') else '不闭合'}")
            log_message(f"    边界边数: {manual_result.get('boundary_edges', 'N/A')}")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log_message("程序被用户中断")
        sys.exit(1)
    except Exception as e:
        log_message(f"程序异常退出: {e}")
        sys.exit(1)
