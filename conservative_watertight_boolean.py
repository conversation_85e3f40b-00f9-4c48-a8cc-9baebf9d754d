#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保守的水密化布尔运算脚本
使用更保守的方法进行网格修复，避免破坏原始几何
"""

import os
import sys
import numpy as np
import re
from datetime import datetime
import traceback

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def read_tsurf_data(file_path):
    """读取TS文件数据"""
    log_message(f"读取TS文件: {os.path.basename(file_path)}")
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                for line in file:
                    line = line.strip()
                    if not line:
                        continue
                    
                    if 'VRTX' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x]
                            if len(nums) >= 4:
                                vertex_id = int(nums[0])
                                x, y, z = float(nums[1]), float(nums[2]), float(nums[3])
                                vrtx.append([x, y, z])
                                vrtx_map[vertex_id] = current_idx
                                current_idx += 1
                        except:
                            continue
                    
                    elif 'TRGL' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x and x.isdigit()]
                            if len(nums) == 3:
                                v1 = vrtx_map[int(nums[0])]
                                v2 = vrtx_map[int(nums[1])]
                                v3 = vrtx_map[int(nums[2])]
                                trgl.append([v1, v2, v3])
                        except:
                            continue
            
            if len(vrtx) > 0 and len(trgl) > 0:
                log_message(f"  成功: {len(vrtx)} 顶点, {len(trgl)} 面")
                return np.array(vrtx), np.array(trgl)
                
        except:
            continue
    
    return np.array([]), np.array([])

def normalize_coordinates(vertices1, vertices2):
    """坐标标准化"""
    all_vertices = np.vstack([vertices1, vertices2])
    min_coords = np.min(all_vertices, axis=0)
    
    vertices1_norm = vertices1 - min_coords
    vertices2_norm = vertices2 - min_coords
    
    log_message(f"坐标偏移: X={min_coords[0]:.1f}, Y={min_coords[1]:.1f}, Z={min_coords[2]:.1f}")
    return vertices1_norm, vertices2_norm, min_coords

def write_obj_file(file_path, vertices, faces):
    """写入OBJ文件"""
    try:
        with open(file_path, 'w') as f:
            for v in vertices:
                f.write(f"v {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            for face in faces:
                f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
        return True
    except Exception as e:
        log_message(f"写入OBJ文件失败: {e}")
        return False

def write_ts_file(file_path, vertices, faces, name="result"):
    """写入TS文件"""
    try:
        with open(file_path, 'w') as f:
            f.write(f"GOCAD TSURF 1\nHEADER {{\nname: {name}\n}}\nTFACE\n")
            for i, v in enumerate(vertices):
                f.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            for face in faces:
                f.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            f.write("END\n")
        return True
    except Exception as e:
        log_message(f"写入TS文件失败: {e}")
        return False

def conservative_mesh_repair(ms, mesh_name):
    """
    保守的网格修复
    只进行必要的修复，不破坏原始几何
    """
    try:
        log_message(f"保守修复: {mesh_name}")
        
        original_vertices = len(ms.current_mesh().vertex_matrix())
        original_faces = len(ms.current_mesh().face_matrix())
        log_message(f"  原始: {original_vertices} 顶点, {original_faces} 面")
        
        # 步骤1: 基础清理
        log_message("  步骤1: 基础清理...")
        ms.meshing_remove_duplicate_vertices()
        ms.meshing_remove_duplicate_faces()
        ms.meshing_remove_null_faces()
        
        # 步骤2: 修复非流形（保守）
        log_message("  步骤2: 修复非流形...")
        try:
            ms.meshing_repair_non_manifold_edges()
            ms.meshing_repair_non_manifold_vertices()
        except Exception as e:
            log_message(f"    非流形修复警告: {e}")
        
        # 步骤3: 保守的孔洞闭合
        log_message("  步骤3: 保守孔洞闭合...")
        try:
            # 只闭合小孔洞，避免破坏几何
            ms.meshing_close_holes(maxholesize=30, selfintersection=False, refinehole=False)
            log_message("    已闭合小孔洞")
            
            # 中等孔洞
            ms.meshing_close_holes(maxholesize=100, selfintersection=False, refinehole=False)
            log_message("    已闭合中等孔洞")
            
        except Exception as e:
            log_message(f"    孔洞闭合警告: {e}")
        
        # 步骤4: 重新定向面法向
        log_message("  步骤4: 统一面法向...")
        try:
            ms.meshing_re_orient_faces_coherently()
        except Exception as e:
            log_message(f"    法向统一警告: {e}")
        
        # 步骤5: 最终清理
        log_message("  步骤5: 最终清理...")
        ms.meshing_remove_duplicate_vertices()
        ms.meshing_remove_unreferenced_vertices()
        
        final_vertices = len(ms.current_mesh().vertex_matrix())
        final_faces = len(ms.current_mesh().face_matrix())
        log_message(f"  最终: {final_vertices} 顶点, {final_faces} 面")
        
        return True
        
    except Exception as e:
        log_message(f"保守修复失败: {e}")
        return False

def try_pymeshlab_boolean_conservative(file1, file2, output_dir, coord_offset):
    """
    使用保守修复的PyMeshLab布尔运算
    """
    try:
        import pymeshlab
        
        log_message("=== PyMeshLab保守布尔运算 ===")
        
        results = {}
        operations = [
            ("intersection", "交集"),
            ("difference", "差集"),
            ("union", "并集")
        ]
        
        for op_type, op_name in operations:
            try:
                log_message(f"\n执行{op_name}运算...")
                
                # 为每个操作创建新的MeshSet
                ms = pymeshlab.MeshSet()
                ms.load_new_mesh(file1)
                ms.load_new_mesh(file2)
                
                # 保守修复第一个网格
                log_message("  保守修复网格1...")
                ms.set_current_mesh(0)
                conservative_mesh_repair(ms, "网格1")
                
                # 保守修复第二个网格
                log_message("  保守修复网格2...")
                ms.set_current_mesh(1)
                conservative_mesh_repair(ms, "网格2")
                
                # 执行布尔运算
                log_message(f"  执行{op_name}...")
                ms.set_current_mesh(0)
                
                if op_type == "intersection":
                    ms.generate_boolean_intersection(first_mesh=0, second_mesh=1)
                elif op_type == "difference":
                    ms.generate_boolean_difference(first_mesh=0, second_mesh=1)
                elif op_type == "union":
                    ms.generate_boolean_union(first_mesh=0, second_mesh=1)
                
                # 删除输入网格，只保留结果
                ms.set_current_mesh(1)
                ms.delete_current_mesh()
                
                # 检查结果
                result_mesh = ms.current_mesh()
                vertex_count = len(result_mesh.vertex_matrix())
                face_count = len(result_mesh.face_matrix())
                
                if vertex_count > 0 and face_count > 0:
                    log_message(f"  ✓ {op_name}成功: {vertex_count} 顶点, {face_count} 面")
                    
                    # 保存结果
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    obj_file = os.path.join(output_dir, f"conservative_{op_type}_{timestamp}.obj")
                    ts_file = os.path.join(output_dir, f"conservative_{op_type}_{timestamp}.ts")
                    
                    # 保存OBJ格式
                    ms.save_current_mesh(obj_file)
                    
                    # 读取结果并转换为TS格式
                    vertices, faces = read_obj_file(obj_file)
                    if len(vertices) > 0:
                        vertices_restored = vertices + coord_offset
                        write_ts_file(ts_file, vertices_restored, faces, f"{op_type}_result")
                        log_message(f"  已保存: {os.path.basename(ts_file)}")
                        
                        results[op_type] = {
                            'success': True,
                            'obj_file': obj_file,
                            'ts_file': ts_file,
                            'vertices': vertex_count,
                            'faces': face_count
                        }
                else:
                    log_message(f"  ✗ {op_name}结果为空")
                    results[op_type] = {'success': False, 'reason': '结果为空'}
                    
            except Exception as e:
                log_message(f"  ✗ {op_name}失败: {e}")
                results[op_type] = {'success': False, 'reason': str(e)}
        
        return results
        
    except Exception as e:
        log_message(f"保守布尔运算整体失败: {e}")
        return {}

def read_obj_file(file_path):
    """读取OBJ文件"""
    vertices = []
    faces = []
    
    try:
        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('v '):
                    parts = line.split()
                    if len(parts) >= 4:
                        vertices.append([float(parts[1]), float(parts[2]), float(parts[3])])
                elif line.startswith('f '):
                    parts = line.split()
                    if len(parts) >= 4:
                        face = []
                        for i in range(1, 4):
                            vertex_idx = int(parts[i].split('/')[0]) - 1
                            face.append(vertex_idx)
                        faces.append(face)
        
        return np.array(vertices), np.array(faces)
    except Exception as e:
        log_message(f"读取OBJ文件失败: {e}")
        return np.array([]), np.array([])

def main():
    """主函数"""
    log_message("=== 保守水密化布尔运算测试 ===")

    # 检查依赖
    try:
        import pymeshlab
        log_message("PyMeshLab已安装")
    except ImportError:
        log_message("错误: PyMeshLab未安装")
        return False

    # 检查输入文件
    file1 = "1_05-J2a_1.ts"
    file2 = "dixing2507101.ts"

    if not os.path.exists(file1) or not os.path.exists(file2):
        log_message("错误: 找不到输入文件")
        return False

    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"conservative_results_{timestamp}"
    temp_dir = f"temp_conservative_{timestamp}"

    try:
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(temp_dir, exist_ok=True)
        log_message(f"输出目录: {output_dir}")

        # 步骤1: 读取TS文件
        log_message("\n=== 步骤1: 读取文件 ===")
        vertices1, faces1 = read_tsurf_data(file1)
        vertices2, faces2 = read_tsurf_data(file2)

        if len(vertices1) == 0 or len(vertices2) == 0:
            log_message("错误: 无法读取输入文件")
            return False

        # 步骤2: 坐标标准化
        log_message("\n=== 步骤2: 坐标标准化 ===")
        vertices1_norm, vertices2_norm, coord_offset = normalize_coordinates(vertices1, vertices2)

        # 步骤3: 保存标准化文件
        log_message("\n=== 步骤3: 保存临时文件 ===")
        temp_obj1 = os.path.join(temp_dir, "mesh1_normalized.obj")
        temp_obj2 = os.path.join(temp_dir, "mesh2_normalized.obj")
        write_obj_file(temp_obj1, vertices1_norm, faces1)
        write_obj_file(temp_obj2, vertices2_norm, faces2)

        # 步骤4: 执行保守布尔运算
        log_message("\n=== 步骤4: 保守布尔运算 ===")
        results = try_pymeshlab_boolean_conservative(temp_obj1, temp_obj2, output_dir, coord_offset)

        # 步骤5: 结果总结
        log_message("\n=== 结果总结 ===")
        success_count = 0

        for op_type in ["intersection", "difference", "union"]:
            if op_type in results and results[op_type]['success']:
                result = results[op_type]
                log_message(f"✓ {op_type}: 成功 (顶点={result['vertices']}, 面={result['faces']})")
                success_count += 1
            else:
                reason = results.get(op_type, {}).get('reason', '未执行')
                log_message(f"✗ {op_type}: 失败 - {reason}")

        # 清理临时文件
        try:
            import shutil
            shutil.rmtree(temp_dir)
        except:
            pass

        log_message(f"\n处理完成！成功: {success_count}/3")
        log_message(f"结果保存在: {output_dir}")

        if success_count > 0:
            log_message("\n🎉 PyMeshLab保守布尔运算成功！")
            log_message("这些结果应该是闭合的网格。")
        else:
            log_message("\n❌ 保守方法也失败了。")
            log_message("建议使用混合方法：PyVista布尔运算 + PyMeshLab后处理。")

        return success_count > 0

    except Exception as e:
        log_message(f"程序执行失败: {e}")
        log_message(traceback.format_exc())
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log_message("程序被用户中断")
        sys.exit(1)
    except Exception as e:
        log_message(f"程序异常退出: {e}")
        sys.exit(1)
