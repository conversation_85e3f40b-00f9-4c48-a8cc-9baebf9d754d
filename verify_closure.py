#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证网格闭合性的简单脚本
检查混合方法生成的结果是否真正闭合
"""

import os
import sys
import numpy as np
from datetime import datetime

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_mesh_closure_manual(obj_file):
    """手动检查网格闭合性（基于边的分析）"""
    try:
        log_message(f"检查: {os.path.basename(obj_file)}")
        
        vertices = []
        faces = []
        
        # 读取OBJ文件
        with open(obj_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('v '):
                    parts = line.split()
                    if len(parts) >= 4:
                        vertices.append([float(parts[1]), float(parts[2]), float(parts[3])])
                elif line.startswith('f '):
                    parts = line.split()
                    if len(parts) >= 4:
                        face = []
                        for i in range(1, 4):
                            vertex_idx = int(parts[i].split('/')[0]) - 1
                            face.append(vertex_idx)
                        faces.append(face)
        
        vertices = np.array(vertices)
        faces = np.array(faces)
        
        log_message(f"  网格: {len(vertices)} 顶点, {len(faces)} 面")
        
        # 构建边字典，统计每条边被多少个面共享
        edge_count = {}
        
        for face in faces:
            # 每个三角面有3条边
            edges = [
                tuple(sorted([face[0], face[1]])),
                tuple(sorted([face[1], face[2]])),
                tuple(sorted([face[2], face[0]]))
            ]
            
            for edge in edges:
                if edge in edge_count:
                    edge_count[edge] += 1
                else:
                    edge_count[edge] = 1
        
        # 分析边的共享情况
        boundary_edges = []  # 只被一个面使用的边（边界边）
        manifold_edges = []  # 被两个面共享的边（流形边）
        non_manifold_edges = []  # 被超过两个面共享的边（非流形边）
        
        for edge, count in edge_count.items():
            if count == 1:
                boundary_edges.append(edge)
            elif count == 2:
                manifold_edges.append(edge)
            else:
                non_manifold_edges.append(edge)
        
        total_edges = len(edge_count)
        boundary_count = len(boundary_edges)
        manifold_count = len(manifold_edges)
        non_manifold_count = len(non_manifold_edges)
        
        log_message(f"  总边数: {total_edges}")
        log_message(f"  边界边: {boundary_count}")
        log_message(f"  流形边: {manifold_count}")
        log_message(f"  非流形边: {non_manifold_count}")
        
        # 判断是否闭合
        is_closed = boundary_count == 0 and non_manifold_count == 0
        
        if is_closed:
            log_message(f"  ✅ 网格是闭合的！")
        else:
            log_message(f"  ❌ 网格不闭合")
            if boundary_count > 0:
                log_message(f"    - 有 {boundary_count} 条边界边")
            if non_manifold_count > 0:
                log_message(f"    - 有 {non_manifold_count} 条非流形边")
        
        return {
            'file': os.path.basename(obj_file),
            'vertices': len(vertices),
            'faces': len(faces),
            'total_edges': total_edges,
            'boundary_edges': boundary_count,
            'manifold_edges': manifold_count,
            'non_manifold_edges': non_manifold_count,
            'is_closed': is_closed
        }
        
    except Exception as e:
        log_message(f"检查失败: {e}")
        return None

def check_with_pyvista(obj_file):
    """使用PyVista检查网格闭合性"""
    try:
        import pyvista as pv
        
        log_message(f"PyVista检查: {os.path.basename(obj_file)}")
        
        mesh = pv.read(obj_file)
        log_message(f"  网格: {mesh.n_points} 顶点, {mesh.n_cells} 面")
        
        # 检查边界边
        edges = mesh.extract_feature_edges(boundary_edges=True, 
                                          non_manifold_edges=False, 
                                          manifold_edges=False)
        
        boundary_edge_count = edges.n_cells if edges.n_cells > 0 else 0
        is_closed = boundary_edge_count == 0
        
        log_message(f"  边界边数: {boundary_edge_count}")
        
        if is_closed:
            log_message(f"  ✅ PyVista确认网格是闭合的！")
            
            # 尝试计算体积
            try:
                volume = mesh.volume
                log_message(f"  体积: {volume:.6f}")
            except:
                log_message(f"  体积: 无法计算")
        else:
            log_message(f"  ❌ PyVista检测到网格不闭合")
        
        return {
            'boundary_edges': boundary_edge_count,
            'is_closed': is_closed,
            'volume': mesh.volume if is_closed else None
        }
        
    except ImportError:
        log_message("PyVista未安装，跳过PyVista检查")
        return None
    except Exception as e:
        log_message(f"PyVista检查失败: {e}")
        return None

def main():
    """主函数"""
    log_message("=== 网格闭合性验证 ===")
    
    # 查找混合结果目录
    result_dirs = [d for d in os.listdir('.') if d.startswith('hybrid_results_')]
    
    if not result_dirs:
        log_message("错误: 找不到混合结果目录")
        return False
    
    # 使用最新的结果目录
    result_dir = sorted(result_dirs)[-1]
    log_message(f"检查目录: {result_dir}")
    
    # 查找OBJ文件
    obj_files = []
    for file in os.listdir(result_dir):
        if file.endswith('.obj'):
            obj_files.append(os.path.join(result_dir, file))
    
    if not obj_files:
        log_message("错误: 找不到OBJ文件")
        return False
    
    log_message(f"找到 {len(obj_files)} 个OBJ文件")
    
    all_closed = True
    
    for obj_file in sorted(obj_files):
        log_message(f"\n{'='*60}")
        
        # 手动检查
        manual_result = check_mesh_closure_manual(obj_file)
        
        # PyVista检查
        pyvista_result = check_with_pyvista(obj_file)
        
        if manual_result and not manual_result['is_closed']:
            all_closed = False
    
    # 总结
    log_message(f"\n{'='*60}")
    log_message("=== 验证总结 ===")
    
    if all_closed:
        log_message("🎉 所有网格都是闭合的！")
        log_message("混合方法成功生成了闭合的布尔运算结果。")
    else:
        log_message("⚠️  部分网格可能不完全闭合。")
        log_message("但这可能仍然比原始的PyVista结果更好。")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log_message("程序被用户中断")
        sys.exit(1)
    except Exception as e:
        log_message(f"程序异常退出: {e}")
        sys.exit(1)
