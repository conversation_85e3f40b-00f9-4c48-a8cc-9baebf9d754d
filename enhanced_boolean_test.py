#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版TS文件布尔运算测试脚本
专门处理非水密网格的布尔运算问题
基于现有代码的最佳实践，增加了网格修复和多种容错策略
"""

import os
import sys
import numpy as np
import re
from datetime import datetime
import traceback

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def read_tsurf_data(file_path):
    """读取TS文件数据"""
    log_message(f"开始读取TS文件: {os.path.basename(file_path)}")
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                for line_num, line in enumerate(file, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    if 'VRTX' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x]
                            if len(nums) >= 4:
                                vertex_id = int(nums[0])
                                x, y, z = float(nums[1]), float(nums[2]), float(nums[3])
                                vrtx.append([x, y, z])
                                vrtx_map[vertex_id] = current_idx
                                current_idx += 1
                        except (ValueError, IndexError) as e:
                            continue
                    
                    elif 'TRGL' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x and x.isdigit()]
                            if len(nums) == 3:
                                v1 = vrtx_map[int(nums[0])]
                                v2 = vrtx_map[int(nums[1])]
                                v3 = vrtx_map[int(nums[2])]
                                trgl.append([v1, v2, v3])
                        except (ValueError, IndexError, KeyError) as e:
                            continue
            
            if len(vrtx) > 0 and len(trgl) > 0:
                log_message(f"成功读取 {os.path.basename(file_path)}，读取了 {len(vrtx)} 个顶点和 {len(trgl)} 个三角面")
                return np.array(vrtx), np.array(trgl)
                
        except UnicodeDecodeError:
            continue
        except Exception as e:
            continue
    
    log_message(f"错误: 无法读取文件 {file_path}")
    return np.array([]), np.array([])

def normalize_coordinates(vertices1, vertices2):
    """坐标标准化"""
    log_message("执行坐标标准化...")
    all_vertices = np.vstack([vertices1, vertices2])
    min_coords = np.min(all_vertices, axis=0)
    
    vertices1_norm = vertices1 - min_coords
    vertices2_norm = vertices2 - min_coords
    
    log_message(f"坐标偏移量: X={min_coords[0]:.2f}, Y={min_coords[1]:.2f}, Z={min_coords[2]:.2f}")
    return vertices1_norm, vertices2_norm, min_coords

def write_obj_file(file_path, vertices, faces):
    """写入OBJ文件"""
    try:
        with open(file_path, 'w') as f:
            for v in vertices:
                f.write(f"v {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            for face in faces:
                f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
        return True
    except Exception as e:
        log_message(f"写入OBJ文件失败: {e}")
        return False

def write_ts_file(file_path, vertices, faces, name="mesh"):
    """写入TS文件"""
    try:
        with open(file_path, 'w') as f:
            f.write(f"GOCAD TSURF 1\nHEADER {{\nname: {name}\n}}\n")
            f.write("TFACE\n")
            for i, v in enumerate(vertices):
                f.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            for face in faces:
                f.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            f.write("END\n")
        return True
    except Exception as e:
        log_message(f"写入TS文件失败: {e}")
        return False

def try_advanced_mesh_repair(obj_file, output_dir):
    """尝试高级网格修复，使网格变为水密"""
    try:
        import pymeshlab
        
        log_message("尝试高级网格修复...")
        ms = pymeshlab.MeshSet()
        ms.load_new_mesh(obj_file)
        
        original_vertices = len(ms.current_mesh().vertex_matrix())
        original_faces = len(ms.current_mesh().face_matrix())
        log_message(f"原始网格: 顶点={original_vertices}, 面={original_faces}")
        
        # 步骤1: 基础清理
        ms.meshing_remove_duplicate_vertices()
        ms.meshing_remove_duplicate_faces()
        ms.meshing_remove_null_faces()  # 替代degenerate_faces
        
        # 步骤2: 修复非流形
        ms.meshing_repair_non_manifold_edges()
        ms.meshing_repair_non_manifold_vertices()
        
        # 步骤3: 尝试闭合孔洞（关键步骤）
        try:
            # 先尝试小孔洞
            ms.meshing_close_holes(maxholesize=30)
            log_message("已闭合小孔洞")
            
            # 再尝试中等孔洞
            ms.meshing_close_holes(maxholesize=100)
            log_message("已闭合中等孔洞")
            
            # 最后尝试大孔洞
            ms.meshing_close_holes(maxholesize=1000)
            log_message("已闭合大孔洞")
            
        except Exception as e:
            log_message(f"孔洞闭合警告: {e}")
        
        # 步骤4: 重新定向面法向
        try:
            ms.meshing_re_orient_faces_coherently()
            log_message("已统一面法向")
        except Exception as e:
            log_message(f"法向统一警告: {e}")
        
        # 步骤5: 最终清理
        ms.meshing_remove_duplicate_vertices()
        ms.meshing_remove_duplicate_faces()
        
        repaired_vertices = len(ms.current_mesh().vertex_matrix())
        repaired_faces = len(ms.current_mesh().face_matrix())
        log_message(f"修复后网格: 顶点={repaired_vertices}, 面={repaired_faces}")
        
        # 保存修复后的网格
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        repaired_file = os.path.join(output_dir, f"repaired_{timestamp}.obj")
        ms.save_current_mesh(repaired_file)
        
        log_message(f"网格修复完成，已保存: {os.path.basename(repaired_file)}")
        return repaired_file
        
    except Exception as e:
        log_message(f"高级网格修复失败: {e}")
        return None

def try_pymeshlab_boolean_with_repair(file1, file2, output_dir, coord_offset):
    """使用修复后的网格尝试PyMeshLab布尔运算"""
    try:
        import pymeshlab
        
        log_message("=== 使用修复策略的PyMeshLab布尔运算 ===")
        
        # 先尝试修复两个网格
        repaired_file1 = try_advanced_mesh_repair(file1, output_dir)
        repaired_file2 = try_advanced_mesh_repair(file2, output_dir)
        
        # 使用修复后的文件（如果修复成功）
        final_file1 = repaired_file1 if repaired_file1 else file1
        final_file2 = repaired_file2 if repaired_file2 else file2
        
        results = {}
        operations = [
            ("intersection", "交集"),
            ("difference", "差集"),
            ("union", "并集")
        ]
        
        for op_type, op_name in operations:
            try:
                log_message(f"执行{op_name}运算...")
                
                ms = pymeshlab.MeshSet()
                ms.load_new_mesh(final_file1)
                ms.load_new_mesh(final_file2)
                
                # 执行布尔运算
                ms.set_current_mesh(0)
                if op_type == "intersection":
                    ms.generate_boolean_intersection(first_mesh=0, second_mesh=1)
                elif op_type == "difference":
                    ms.generate_boolean_difference(first_mesh=0, second_mesh=1)
                elif op_type == "union":
                    ms.generate_boolean_union(first_mesh=0, second_mesh=1)
                
                # 删除第二个网格
                ms.set_current_mesh(1)
                ms.delete_current_mesh()
                
                # 检查结果
                result_mesh = ms.current_mesh()
                vertex_count = len(result_mesh.vertex_matrix())
                face_count = len(result_mesh.face_matrix())
                
                if vertex_count > 0 and face_count > 0:
                    # 保存结果
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    obj_file = os.path.join(output_dir, f"repaired_{op_type}_{timestamp}.obj")
                    ts_file = os.path.join(output_dir, f"repaired_{op_type}_{timestamp}.ts")
                    
                    ms.save_current_mesh(obj_file)
                    
                    # 读取结果并转换为TS格式
                    vertices, faces = read_obj_file(obj_file)
                    if len(vertices) > 0:
                        vertices_restored = vertices + coord_offset
                        write_ts_file(ts_file, vertices_restored, faces, f"{op_type}_result")
                        log_message(f"  ✓ {op_name}成功: 顶点={vertex_count}, 面={face_count}")
                        
                        results[op_type] = {
                            'success': True,
                            'obj_file': obj_file,
                            'ts_file': ts_file,
                            'vertices': vertex_count,
                            'faces': face_count
                        }
                else:
                    log_message(f"  ✗ {op_name}结果为空")
                    results[op_type] = {'success': False, 'reason': '结果为空'}
                    
            except Exception as e:
                log_message(f"  ✗ {op_name}失败: {e}")
                results[op_type] = {'success': False, 'reason': str(e)}
        
        return results
        
    except Exception as e:
        log_message(f"修复策略布尔运算整体失败: {e}")
        return {}

def read_obj_file(file_path):
    """读取OBJ文件"""
    vertices = []
    faces = []
    
    try:
        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('v '):
                    parts = line.split()
                    if len(parts) >= 4:
                        vertices.append([float(parts[1]), float(parts[2]), float(parts[3])])
                elif line.startswith('f '):
                    parts = line.split()
                    if len(parts) >= 4:
                        face = []
                        for i in range(1, 4):
                            vertex_idx = int(parts[i].split('/')[0]) - 1
                            face.append(vertex_idx)
                        faces.append(face)
        
        return np.array(vertices), np.array(faces)
    except Exception as e:
        log_message(f"读取OBJ文件失败: {e}")
        return np.array([]), np.array([])

def try_pyvista_boolean_operations(vertices1, faces1, vertices2, faces2, output_dir, coord_offset):
    """使用PyVista尝试布尔运算"""
    try:
        import pyvista as pv
        
        log_message("=== 尝试PyVista布尔运算 ===")
        
        # 创建PyVista网格
        faces_with_header1 = []
        for face in faces1:
            faces_with_header1.extend([3, face[0], face[1], face[2]])
        mesh1 = pv.PolyData(vertices1, np.array(faces_with_header1))
        
        faces_with_header2 = []
        for face in faces2:
            faces_with_header2.extend([3, face[0], face[1], face[2]])
        mesh2 = pv.PolyData(vertices2, np.array(faces_with_header2))
        
        log_message(f"网格1: 顶点={mesh1.n_points}, 面={mesh1.n_cells}")
        log_message(f"网格2: 顶点={mesh2.n_points}, 面={mesh2.n_cells}")
        
        results = {}
        operations = [
            ("intersection", "交集", "boolean_intersection"),
            ("difference", "差集", "boolean_difference"),
            ("union", "并集", "boolean_union")
        ]
        
        for op_type, op_name, method_name in operations:
            try:
                log_message(f"执行{op_name}运算...")
                
                method = getattr(mesh1, method_name)
                result_mesh = method(mesh2)
                
                if result_mesh.n_points > 0 and result_mesh.n_cells > 0:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    obj_file = os.path.join(output_dir, f"pyvista_{op_type}_{timestamp}.obj")
                    ts_file = os.path.join(output_dir, f"pyvista_{op_type}_{timestamp}.ts")
                    
                    result_mesh.save(obj_file)
                    
                    vertices = result_mesh.points
                    faces = result_mesh.faces.reshape(-1, 4)[:, 1:4]
                    vertices_restored = vertices + coord_offset
                    
                    write_ts_file(ts_file, vertices_restored, faces, f"{op_type}_result")
                    log_message(f"  ✓ {op_name}成功: 顶点={result_mesh.n_points}, 面={result_mesh.n_cells}")
                    
                    results[op_type] = {
                        'success': True,
                        'obj_file': obj_file,
                        'ts_file': ts_file,
                        'vertices': result_mesh.n_points,
                        'faces': result_mesh.n_cells
                    }
                else:
                    log_message(f"  ✗ {op_name}结果为空")
                    results[op_type] = {'success': False, 'reason': '结果为空'}
                    
            except Exception as e:
                log_message(f"  ✗ {op_name}失败: {e}")
                results[op_type] = {'success': False, 'reason': str(e)}
        
        return results
        
    except ImportError:
        log_message("警告: PyVista未安装")
        return {}
    except Exception as e:
        log_message(f"PyVista布尔运算失败: {e}")
        return {}

def main():
    """主函数"""
    log_message("=== 增强版TS文件布尔运算测试 ===")

    # 检查文件
    file1 = "1_05-J2a_1.ts"
    file2 = "dixing2507101.ts"

    if not os.path.exists(file1) or not os.path.exists(file2):
        log_message("错误: 找不到输入文件")
        return False

    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"enhanced_results_{timestamp}"
    temp_dir = f"temp_enhanced_{timestamp}"

    try:
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(temp_dir, exist_ok=True)
        log_message(f"创建输出目录: {output_dir}")

        # 读取文件
        log_message("\n=== 步骤1: 读取TS文件 ===")
        vertices1, faces1 = read_tsurf_data(file1)
        vertices2, faces2 = read_tsurf_data(file2)

        if len(vertices1) == 0 or len(vertices2) == 0:
            log_message("错误: 无法读取输入文件")
            return False

        # 坐标标准化
        log_message("\n=== 步骤2: 坐标标准化 ===")
        vertices1_norm, vertices2_norm, coord_offset = normalize_coordinates(vertices1, vertices2)

        # 保存标准化文件
        temp_obj1 = os.path.join(temp_dir, "mesh1_normalized.obj")
        temp_obj2 = os.path.join(temp_dir, "mesh2_normalized.obj")
        write_obj_file(temp_obj1, vertices1_norm, faces1)
        write_obj_file(temp_obj2, vertices2_norm, faces2)

        # 尝试修复策略的PyMeshLab布尔运算
        log_message("\n=== 步骤3: 修复策略布尔运算 ===")
        repair_results = try_pymeshlab_boolean_with_repair(temp_obj1, temp_obj2, output_dir, coord_offset)

        # 尝试PyVista布尔运算
        log_message("\n=== 步骤4: PyVista布尔运算 ===")
        pyvista_results = try_pyvista_boolean_operations(
            vertices1_norm, faces1, vertices2_norm, faces2, output_dir, coord_offset
        )

        # 结果总结
        log_message("\n=== 处理结果总结 ===")
        success_count = 0

        log_message("修复策略结果:")
        for op_type in ["intersection", "difference", "union"]:
            if op_type in repair_results and repair_results[op_type]['success']:
                result = repair_results[op_type]
                log_message(f"  ✓ {op_type}: 成功 (顶点={result['vertices']}, 面={result['faces']})")
                success_count += 1
            else:
                reason = repair_results.get(op_type, {}).get('reason', '未执行')
                log_message(f"  ✗ {op_type}: 失败 - {reason}")

        log_message("PyVista结果:")
        for op_type in ["intersection", "difference", "union"]:
            if op_type in pyvista_results and pyvista_results[op_type]['success']:
                result = pyvista_results[op_type]
                log_message(f"  ✓ {op_type}: 成功 (顶点={result['vertices']}, 面={result['faces']})")
                success_count += 1
            else:
                reason = pyvista_results.get(op_type, {}).get('reason', '未执行')
                log_message(f"  ✗ {op_type}: 失败 - {reason}")

        # 清理临时文件
        try:
            import shutil
            shutil.rmtree(temp_dir)
        except:
            pass

        log_message(f"\n处理完成！结果保存在: {output_dir}")
        log_message(f"成功的操作数: {success_count}/6")

        if success_count > 0:
            log_message("\n✓ 至少有一些布尔运算成功了！")
            log_message("建议检查输出目录中的结果文件。")
        else:
            log_message("\n✗ 所有布尔运算都失败了。")
            log_message("这可能是因为网格几何复杂或存在严重的拓扑问题。")

        return success_count > 0

    except Exception as e:
        log_message(f"主程序执行失败: {e}")
        log_message(traceback.format_exc())
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log_message("程序被用户中断")
        sys.exit(1)
    except Exception as e:
        log_message(f"程序异常退出: {e}")
        sys.exit(1)
