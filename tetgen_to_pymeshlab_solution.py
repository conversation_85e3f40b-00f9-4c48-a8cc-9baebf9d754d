#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于TetGen的PyMeshLab布尔运算解决方案
将表面网格转换为四面体网格，然后使用PyMeshLab进行布尔运算
"""

import os
import sys
import numpy as np
import re
from datetime import datetime
import traceback

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def read_tsurf_data(file_path):
    """读取TS文件数据"""
    log_message(f"读取TS文件: {os.path.basename(file_path)}")
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                for line in file:
                    line = line.strip()
                    if not line:
                        continue
                    
                    if 'VRTX' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x]
                            if len(nums) >= 4:
                                vertex_id = int(nums[0])
                                x, y, z = float(nums[1]), float(nums[2]), float(nums[3])
                                vrtx.append([x, y, z])
                                vrtx_map[vertex_id] = current_idx
                                current_idx += 1
                        except:
                            continue
                    
                    elif 'TRGL' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x and x.isdigit()]
                            if len(nums) == 3:
                                v1 = vrtx_map[int(nums[0])]
                                v2 = vrtx_map[int(nums[1])]
                                v3 = vrtx_map[int(nums[2])]
                                trgl.append([v1, v2, v3])
                        except:
                            continue
            
            if len(vrtx) > 0 and len(trgl) > 0:
                log_message(f"  成功: {len(vrtx)} 顶点, {len(trgl)} 面")
                return np.array(vrtx), np.array(trgl)
                
        except:
            continue
    
    return np.array([]), np.array([])

def normalize_coordinates(vertices1, vertices2):
    """坐标标准化"""
    all_vertices = np.vstack([vertices1, vertices2])
    min_coords = np.min(all_vertices, axis=0)
    
    vertices1_norm = vertices1 - min_coords
    vertices2_norm = vertices2 - min_coords
    
    log_message(f"坐标偏移: X={min_coords[0]:.1f}, Y={min_coords[1]:.1f}, Z={min_coords[2]:.1f}")
    return vertices1_norm, vertices2_norm, min_coords

def surface_to_tetrahedral_mesh(vertices, faces, mesh_name):
    """
    使用TetGen将表面网格转换为四面体网格
    """
    try:
        import tetgen
        
        log_message(f"将{mesh_name}转换为四面体网格...")
        
        # 创建TetGen输入
        tet = tetgen.TetGen(vertices, faces)
        
        # 生成四面体网格
        # 参数说明：
        # pq1.414a0.1 - p: 四面体化, q: 质量网格, 1.414: 最小角度, a0.1: 最大体积
        log_message(f"  执行四面体化...")
        tet.tetrahedralize('pq1.414a0.01')  # 使用较小的体积约束确保质量
        
        # 获取结果
        tet_vertices = tet.node
        tet_faces = tet.face  # 表面三角面
        tet_cells = tet.elem  # 四面体单元
        
        log_message(f"  四面体网格结果:")
        log_message(f"    顶点: {len(tet_vertices)}")
        log_message(f"    表面面: {len(tet_faces)}")
        log_message(f"    四面体: {len(tet_cells)}")
        
        return tet_vertices, tet_faces, tet_cells
        
    except ImportError:
        log_message("错误: tetgen未安装，请安装: pip install tetgen")
        return None, None, None
    except Exception as e:
        log_message(f"四面体网格生成失败: {e}")
        return None, None, None

def save_tetrahedral_mesh_as_vtk(vertices, cells, file_path):
    """
    将四面体网格保存为VTK格式
    """
    try:
        import pyvista as pv
        
        log_message(f"保存四面体网格为VTK: {os.path.basename(file_path)}")
        
        # 创建四面体网格的VTK格式
        # 每个四面体需要5个数字：4（表示四面体）+ 4个顶点索引
        cells_vtk = []
        for cell in cells:
            cells_vtk.extend([4, cell[0], cell[1], cell[2], cell[3]])
        
        # 创建PyVista网格
        mesh = pv.UnstructuredGrid(np.array(cells_vtk), np.array([10] * len(cells)), vertices)
        
        # 保存为VTK文件
        mesh.save(file_path)
        log_message(f"  已保存: {os.path.basename(file_path)}")
        
        return True
        
    except ImportError:
        log_message("错误: PyVista未安装")
        return False
    except Exception as e:
        log_message(f"保存VTK文件失败: {e}")
        return False

def vtk_to_pymeshlab_boolean(vtk_file1, vtk_file2, output_dir, coord_offset):
    """
    使用PyMeshLab对VTK四面体网格进行布尔运算
    """
    try:
        import pymeshlab
        
        log_message("=== PyMeshLab VTK布尔运算 ===")
        
        results = {}
        operations = [
            ("intersection", "交集"),
            ("difference", "差集"),
            ("union", "并集")
        ]
        
        for op_type, op_name in operations:
            try:
                log_message(f"\n执行{op_name}运算...")
                
                # 创建新的MeshSet
                ms = pymeshlab.MeshSet()
                
                # 尝试加载VTK文件
                try:
                    ms.load_new_mesh(vtk_file1)
                    ms.load_new_mesh(vtk_file2)
                    log_message(f"  成功加载VTK文件")
                except Exception as e:
                    log_message(f"  VTK加载失败，尝试转换为OBJ: {e}")
                    # 如果VTK加载失败，转换为OBJ
                    obj_file1 = vtk_to_obj(vtk_file1)
                    obj_file2 = vtk_to_obj(vtk_file2)
                    if obj_file1 and obj_file2:
                        ms.load_new_mesh(obj_file1)
                        ms.load_new_mesh(obj_file2)
                        log_message(f"  通过OBJ转换成功加载")
                    else:
                        raise Exception("VTK和OBJ转换都失败")
                
                # 记录网格信息
                ms.set_current_mesh(0)
                m1 = ms.current_mesh()
                log_message(f"  网格1: 顶点={len(m1.vertex_matrix())}, 面={len(m1.face_matrix())}")
                
                ms.set_current_mesh(1)
                m2 = ms.current_mesh()
                log_message(f"  网格2: 顶点={len(m2.vertex_matrix())}, 面={len(m2.face_matrix())}")
                
                # 执行布尔运算
                ms.set_current_mesh(0)
                if op_type == "intersection":
                    ms.generate_boolean_intersection(first_mesh=0, second_mesh=1)
                elif op_type == "difference":
                    ms.generate_boolean_difference(first_mesh=0, second_mesh=1)
                elif op_type == "union":
                    ms.generate_boolean_union(first_mesh=0, second_mesh=1)
                
                # 删除第二个网格
                ms.set_current_mesh(1)
                ms.delete_current_mesh()
                
                # 检查结果
                result_mesh = ms.current_mesh()
                vertex_count = len(result_mesh.vertex_matrix())
                face_count = len(result_mesh.face_matrix())
                
                if vertex_count > 0 and face_count > 0:
                    log_message(f"  ✓ {op_name}成功: {vertex_count} 顶点, {face_count} 面")
                    
                    # 保存结果
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    obj_file = os.path.join(output_dir, f"tetgen_{op_type}_{timestamp}.obj")
                    ts_file = os.path.join(output_dir, f"tetgen_{op_type}_{timestamp}.ts")
                    
                    # 保存OBJ格式
                    ms.save_current_mesh(obj_file)
                    
                    # 转换为TS格式
                    vertices, faces = read_obj_file(obj_file)
                    if len(vertices) > 0:
                        vertices_restored = vertices + coord_offset
                        write_ts_file(ts_file, vertices_restored, faces, f"tetgen_{op_type}_result")
                        log_message(f"  已保存: {os.path.basename(ts_file)}")
                        
                        results[op_type] = {
                            'success': True,
                            'obj_file': obj_file,
                            'ts_file': ts_file,
                            'vertices': vertex_count,
                            'faces': face_count
                        }
                else:
                    log_message(f"  ✗ {op_name}结果为空")
                    results[op_type] = {'success': False, 'reason': '结果为空'}
                    
            except Exception as e:
                log_message(f"  ✗ {op_name}失败: {e}")
                results[op_type] = {'success': False, 'reason': str(e)}
        
        return results
        
    except ImportError:
        log_message("错误: PyMeshLab未安装")
        return {}
    except Exception as e:
        log_message(f"PyMeshLab VTK布尔运算失败: {e}")
        return {}

def vtk_to_obj(vtk_file):
    """将VTK文件转换为OBJ文件"""
    try:
        import pyvista as pv
        
        # 读取VTK文件
        mesh = pv.read(vtk_file)
        
        # 提取表面
        surface = mesh.extract_surface()
        
        # 保存为OBJ
        obj_file = vtk_file.replace('.vtk', '.obj')
        surface.save(obj_file)
        
        return obj_file
        
    except Exception as e:
        log_message(f"VTK到OBJ转换失败: {e}")
        return None

def read_obj_file(file_path):
    """读取OBJ文件"""
    vertices = []
    faces = []
    
    try:
        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('v '):
                    parts = line.split()
                    if len(parts) >= 4:
                        vertices.append([float(parts[1]), float(parts[2]), float(parts[3])])
                elif line.startswith('f '):
                    parts = line.split()
                    if len(parts) >= 4:
                        face = []
                        for i in range(1, 4):
                            vertex_idx = int(parts[i].split('/')[0]) - 1
                            face.append(vertex_idx)
                        faces.append(face)
        
        return np.array(vertices), np.array(faces)
    except Exception as e:
        log_message(f"读取OBJ文件失败: {e}")
        return np.array([]), np.array([])

def write_ts_file(file_path, vertices, faces, name="result"):
    """写入TS文件"""
    try:
        with open(file_path, 'w') as f:
            f.write(f"GOCAD TSurf 1\n")
            f.write(f"HEADER {{\n")
            f.write(f"name: {name}\n")
            f.write(f"}}\n")
            f.write("TFACE\n")
            
            for i, v in enumerate(vertices):
                f.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            
            for face in faces:
                f.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            f.write("END\n")
        
        return True
    except Exception as e:
        log_message(f"写入TS文件失败: {e}")
        return False

def main():
    """主函数"""
    log_message("=== TetGen + PyMeshLab 布尔运算解决方案 ===")
    log_message("表面网格 -> 四面体网格 -> VTK -> PyMeshLab布尔运算")

    # 检查依赖
    try:
        import tetgen
        import pymeshlab
        import pyvista as pv
        log_message("所有依赖已安装")
    except ImportError as e:
        log_message(f"错误: 缺少依赖 - {e}")
        log_message("请安装: pip install tetgen pymeshlab pyvista")
        return False

    # 检查输入文件
    file1 = "1_05-J2a_1.ts"
    file2 = "dixing2507101.ts"

    if not os.path.exists(file1) or not os.path.exists(file2):
        log_message("错误: 找不到输入文件")
        return False

    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"tetgen_results_{timestamp}"
    temp_dir = f"temp_tetgen_{timestamp}"

    try:
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(temp_dir, exist_ok=True)
        log_message(f"输出目录: {output_dir}")

        # 步骤1: 读取TS文件
        log_message("\n=== 步骤1: 读取TS文件 ===")
        vertices1, faces1 = read_tsurf_data(file1)
        vertices2, faces2 = read_tsurf_data(file2)

        if len(vertices1) == 0 or len(vertices2) == 0:
            log_message("错误: 无法读取输入文件")
            return False

        # 步骤2: 坐标标准化
        log_message("\n=== 步骤2: 坐标标准化 ===")
        vertices1_norm, vertices2_norm, coord_offset = normalize_coordinates(vertices1, vertices2)

        # 步骤3: 转换为四面体网格
        log_message("\n=== 步骤3: 生成四面体网格 ===")
        tet_vertices1, tet_faces1, tet_cells1 = surface_to_tetrahedral_mesh(vertices1_norm, faces1, "文件1")
        tet_vertices2, tet_faces2, tet_cells2 = surface_to_tetrahedral_mesh(vertices2_norm, faces2, "文件2")

        if tet_vertices1 is None or tet_vertices2 is None:
            log_message("错误: 四面体网格生成失败")
            return False

        # 步骤4: 保存为VTK格式
        log_message("\n=== 步骤4: 保存VTK文件 ===")
        vtk_file1 = os.path.join(temp_dir, "mesh1_tet.vtk")
        vtk_file2 = os.path.join(temp_dir, "mesh2_tet.vtk")

        if not save_tetrahedral_mesh_as_vtk(tet_vertices1, tet_cells1, vtk_file1):
            log_message("错误: VTK文件1保存失败")
            return False

        if not save_tetrahedral_mesh_as_vtk(tet_vertices2, tet_cells2, vtk_file2):
            log_message("错误: VTK文件2保存失败")
            return False

        # 步骤5: PyMeshLab布尔运算
        log_message("\n=== 步骤5: PyMeshLab VTK布尔运算 ===")
        results = vtk_to_pymeshlab_boolean(vtk_file1, vtk_file2, output_dir, coord_offset)

        # 步骤6: 结果总结
        log_message("\n=== 结果总结 ===")
        success_count = 0

        for op_type in ["intersection", "difference", "union"]:
            if op_type in results and results[op_type]['success']:
                result = results[op_type]
                log_message(f"✓ {op_type}: 成功 (顶点={result['vertices']}, 面={result['faces']})")
                success_count += 1
            else:
                reason = results.get(op_type, {}).get('reason', '未执行')
                log_message(f"✗ {op_type}: 失败 - {reason}")

        # 清理临时文件
        try:
            import shutil
            shutil.rmtree(temp_dir)
        except:
            pass

        log_message(f"\n处理完成！成功: {success_count}/3")
        log_message(f"结果保存在: {output_dir}")

        if success_count > 0:
            log_message("\n🎉 TetGen + PyMeshLab 方案成功！")
            log_message("通过四面体网格化解决了PyMeshLab的水密性要求。")
            log_message("结果应该是完全闭合的。")
        else:
            log_message("\n❌ TetGen方案也失败了。")
            log_message("可能需要调整四面体网格参数或尝试其他方法。")

        return success_count > 0

    except Exception as e:
        log_message(f"程序执行失败: {e}")
        log_message(traceback.format_exc())
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log_message("程序被用户中断")
        sys.exit(1)
    except Exception as e:
        log_message(f"程序异常退出: {e}")
        sys.exit(1)
