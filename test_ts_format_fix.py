#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的TS格式导出功能
"""

import os
import sys
import numpy as np
from datetime import datetime

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_ts_format_export():
    """测试TS格式导出"""
    log_message("=== 测试修复后的TS格式导出功能 ===")
    
    # 创建测试数据
    test_vertices = np.array([
        [0.0, 0.0, 0.0],
        [1.0, 0.0, 0.0],
        [0.5, 1.0, 0.0],
        [0.5, 0.5, 1.0]
    ])
    
    test_faces = np.array([
        [0, 1, 2],
        [0, 1, 3],
        [1, 2, 3],
        [0, 2, 3]
    ])
    
    log_message(f"测试数据: {len(test_vertices)} 顶点, {len(test_faces)} 面")
    
    # 创建测试TS文件
    test_ts_file = "test_output.ts"
    
    try:
        # 手动写入正确的GOCAD TS格式
        with open(test_ts_file, "w") as file:
            # 写入GOCAD TS文件头
            file.write("GOCAD TSurf 1\n")
            file.write("HEADER {\n")
            file.write("name: test_output\n")
            file.write("}\n")
            file.write("TFACE\n")
            
            # 写入顶点(TS使用1-based索引)
            for i, v in enumerate(test_vertices):
                file.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            
            # 写入面(TS索引从1开始)
            for face in test_faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            # 写入结束标记
            file.write("END\n")
        
        log_message(f"✓ 成功创建测试TS文件: {test_ts_file}")
        
        # 验证文件内容
        with open(test_ts_file, "r") as file:
            content = file.read()
        
        log_message("生成的TS文件内容:")
        print("=" * 50)
        print(content)
        print("=" * 50)
        
        # 检查格式是否正确
        lines = content.strip().split('\n')
        
        # 检查文件头
        if lines[0] == "GOCAD TSurf 1":
            log_message("✓ 文件头格式正确")
        else:
            log_message("✗ 文件头格式错误")
            return False
        
        # 检查HEADER部分
        if "HEADER {" in content and "name: test_output" in content and "}" in content:
            log_message("✓ HEADER部分格式正确")
        else:
            log_message("✗ HEADER部分格式错误")
            return False
        
        # 检查TFACE标记
        if "TFACE" in content:
            log_message("✓ TFACE标记存在")
        else:
            log_message("✗ TFACE标记缺失")
            return False
        
        # 检查顶点格式
        vrtx_lines = [line for line in lines if line.startswith("VRTX")]
        if len(vrtx_lines) == len(test_vertices):
            log_message(f"✓ 顶点数量正确: {len(vrtx_lines)}")
            
            # 检查第一个顶点的格式
            first_vrtx = vrtx_lines[0]
            if first_vrtx == "VRTX 1 0.000000 0.000000 0.000000":
                log_message("✓ 顶点格式正确")
            else:
                log_message(f"✗ 顶点格式错误: {first_vrtx}")
                return False
        else:
            log_message(f"✗ 顶点数量错误: 期望{len(test_vertices)}, 实际{len(vrtx_lines)}")
            return False
        
        # 检查面格式
        trgl_lines = [line for line in lines if line.startswith("TRGL")]
        if len(trgl_lines) == len(test_faces):
            log_message(f"✓ 面数量正确: {len(trgl_lines)}")
            
            # 检查第一个面的格式
            first_trgl = trgl_lines[0]
            if first_trgl == "TRGL 1 2 3":
                log_message("✓ 面格式正确")
            else:
                log_message(f"✗ 面格式错误: {first_trgl}")
                return False
        else:
            log_message(f"✗ 面数量错误: 期望{len(test_faces)}, 实际{len(trgl_lines)}")
            return False
        
        # 检查结束标记
        if lines[-1] == "END":
            log_message("✓ 结束标记正确")
        else:
            log_message("✗ 结束标记错误")
            return False
        
        # 清理测试文件
        try:
            os.remove(test_ts_file)
            log_message("✓ 测试文件已清理")
        except:
            pass
        
        return True
        
    except Exception as e:
        log_message(f"✗ 测试失败: {e}")
        return False

def compare_with_original_ts():
    """与原始TS文件格式对比"""
    log_message("\n=== 与原始TS文件格式对比 ===")
    
    original_file = "1_05-J2a_1.ts"
    if not os.path.exists(original_file):
        log_message("✗ 原始TS文件不存在")
        return False
    
    try:
        with open(original_file, "r", encoding="utf-8") as file:
            lines = file.readlines()[:20]  # 只读前20行
        
        log_message("原始TS文件格式示例:")
        print("=" * 50)
        for i, line in enumerate(lines, 1):
            print(f"{i:2d}: {line.rstrip()}")
        print("=" * 50)
        
        # 检查关键格式元素
        content = ''.join(lines)
        
        if "GOCAD TSurf 1" in content:
            log_message("✓ 原始文件使用GOCAD TSurf格式")
        else:
            log_message("✗ 原始文件格式异常")
            return False
        
        if "HEADER {" in content and "name:" in content:
            log_message("✓ 原始文件有标准HEADER格式")
        else:
            log_message("✗ 原始文件HEADER格式异常")
            return False
        
        return True
        
    except Exception as e:
        log_message(f"✗ 读取原始文件失败: {e}")
        return False

def main():
    """主函数"""
    log_message("开始测试TS格式修复...")
    
    format_test = test_ts_format_export()
    comparison_test = compare_with_original_ts()
    
    if format_test and comparison_test:
        log_message("\n🎉 TS格式修复测试成功！")
        log_message("✓ 修复后的write_ts_file函数生成标准GOCAD TS格式")
        log_message("✓ 格式与原始TS文件一致")
        log_message("✓ 现在布尔运算结果将生成正确的TS文件")
        log_message("\n修复内容:")
        log_message("1. 添加了GOCAD TSurf 1文件头")
        log_message("2. 添加了标准HEADER部分")
        log_message("3. 添加了TFACE标记")
        log_message("4. 使用正确的VRTX和TRGL格式")
        log_message("5. 添加了END结束标记")
    else:
        log_message("\n❌ TS格式修复测试失败")
        if not format_test:
            log_message("- 格式生成测试失败")
        if not comparison_test:
            log_message("- 与原始文件对比失败")
    
    return format_test and comparison_test

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        log_message(f"程序异常: {e}")
        sys.exit(1)
