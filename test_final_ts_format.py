#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复的TS格式
"""

import os
import sys
import numpy as np
from datetime import datetime

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_final_ts_format():
    """测试最终的TS格式"""
    log_message("=== 测试最终修复的TS格式 ===")
    
    # 创建测试数据
    test_vertices = np.array([
        [0.0, 0.0, 0.0],
        [1.0, 0.0, 0.0],
        [0.5, 1.0, 0.0]
    ])
    
    test_faces = np.array([
        [0, 1, 2]
    ])
    
    # 创建测试TS文件
    test_ts_file = "final_test.ts"
    
    try:
        # 使用修复后的格式
        with open(test_ts_file, "w") as file:
            # 写入GOCAD TS文件头（注意末尾的空格）
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write("name: final_test\n")
            file.write("}\n")
            file.write("TFACE\n")
            
            # 写入顶点(TS使用1-based索引)
            for i, v in enumerate(test_vertices):
                file.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            
            # 写入面(TS索引从1开始)
            for face in test_faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            # 写入结束标记
            file.write("END\n")
        
        log_message(f"✓ 成功创建测试TS文件: {test_ts_file}")
        
        # 读取并显示内容
        with open(test_ts_file, "r") as file:
            content = file.read()
        
        log_message("生成的TS文件内容:")
        print("=" * 50)
        print(content)
        print("=" * 50)
        
        # 与原始文件对比第一行
        original_file = "1_05-J2a_1.ts"
        if os.path.exists(original_file):
            with open(original_file, "r", encoding="utf-8") as f:
                original_first_line = f.readline().rstrip('\r\n')
            
            test_first_line = content.split('\n')[0]
            
            log_message(f"原始文件第一行: '{original_first_line}'")
            log_message(f"测试文件第一行: '{test_first_line}'")
            
            if original_first_line == test_first_line:
                log_message("✓ 第一行格式完全匹配")
            else:
                log_message("✗ 第一行格式不匹配")
                log_message(f"  原始长度: {len(original_first_line)}")
                log_message(f"  测试长度: {len(test_first_line)}")
                return False
        
        # 清理测试文件
        try:
            os.remove(test_ts_file)
        except:
            pass
        
        return True
        
    except Exception as e:
        log_message(f"✗ 测试失败: {e}")
        return False

def main():
    """主函数"""
    log_message("开始测试最终TS格式修复...")
    
    success = test_final_ts_format()
    
    if success:
        log_message("\n🎉 最终TS格式修复测试成功！")
        log_message("✓ 文件头格式与原始文件完全一致")
        log_message("✓ 现在所有布尔运算都将生成正确的GOCAD TS格式")
        log_message("\n修复要点:")
        log_message("1. 文件头: 'GOCAD TSurf 1 ' (注意末尾空格)")
        log_message("2. HEADER部分格式正确")
        log_message("3. TFACE标记")
        log_message("4. VRTX和TRGL使用1-based索引")
        log_message("5. END结束标记")
    else:
        log_message("\n❌ 最终TS格式修复测试失败")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        log_message(f"程序异常: {e}")
        sys.exit(1)
