#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的TS文件布尔运算测试脚本
专门用于处理大坐标值的TS文件（如1_05-J2a_1.ts和dixing2507101.ts）
基于现有代码的最佳实践，增强了错误处理和坐标标准化功能
"""

import os
import sys
import numpy as np
import re
from datetime import datetime
import traceback

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def read_tsurf_data(file_path):
    """
    读取TS文件（TSURF格式）数据
    基于ui_integrated_w5.py中的read_tsurf_data函数，增强了错误处理
    """
    log_message(f"开始读取TS文件: {os.path.basename(file_path)}")
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                for line_num, line in enumerate(file, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    # 处理VRTX行
                    if 'VRTX' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            # 过滤掉空字符串
                            nums = [x for x in l_input[1:] if x]
                            if len(nums) >= 4:
                                vertex_id = int(nums[0])
                                x, y, z = float(nums[1]), float(nums[2]), float(nums[3])
                                vrtx.append([x, y, z])
                                vrtx_map[vertex_id] = current_idx
                                current_idx += 1
                        except (ValueError, IndexError) as e:
                            log_message(f"处理顶点时出错，行 {line_num}: {e}")
                            continue
                    
                    # 处理TRGL行
                    elif 'TRGL' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x and x.isdigit()]
                            if len(nums) == 3:
                                v1 = vrtx_map[int(nums[0])]
                                v2 = vrtx_map[int(nums[1])]
                                v3 = vrtx_map[int(nums[2])]
                                trgl.append([v1, v2, v3])
                        except (ValueError, IndexError, KeyError) as e:
                            log_message(f"处理三角面时出错，行 {line_num}: {e}")
                            continue
            
            if len(vrtx) > 0 and len(trgl) > 0:
                log_message(f"成功读取 {os.path.basename(file_path)}，使用编码 {encoding}")
                log_message(f"读取了 {len(vrtx)} 个顶点和 {len(trgl)} 个三角面")
                return np.array(vrtx), np.array(trgl)
                
        except UnicodeDecodeError:
            log_message(f"无法使用 {encoding} 编码读取文件，尝试下一种编码...")
            continue
        except Exception as e:
            log_message(f"读取文件时发生意外错误: {e}")
            continue
    
    log_message(f"错误: 无法读取文件 {file_path}")
    return np.array([]), np.array([])

def analyze_mesh_coordinates(vertices, mesh_name):
    """分析网格坐标范围"""
    if len(vertices) == 0:
        return None
    
    min_coords = np.min(vertices, axis=0)
    max_coords = np.max(vertices, axis=0)
    center = np.mean(vertices, axis=0)
    max_coord_value = np.max(np.abs(vertices))
    
    log_message(f"{mesh_name} 坐标分析:")
    log_message(f"  最小坐标: X={min_coords[0]:.2f}, Y={min_coords[1]:.2f}, Z={min_coords[2]:.2f}")
    log_message(f"  最大坐标: X={max_coords[0]:.2f}, Y={max_coords[1]:.2f}, Z={max_coords[2]:.2f}")
    log_message(f"  中心坐标: X={center[0]:.2f}, Y={center[1]:.2f}, Z={center[2]:.2f}")
    log_message(f"  最大坐标值: {max_coord_value:.2f}")
    
    return {
        'min': min_coords,
        'max': max_coords,
        'center': center,
        'max_value': max_coord_value
    }

def normalize_coordinates(vertices1, vertices2):
    """
    坐标标准化，将两个网格的坐标移动到原点附近
    基于ui_integrated_w5.py中的normalize_mesh_coordinates函数
    """
    log_message("执行坐标标准化...")
    
    # 计算综合的最小值作为平移参考点
    all_vertices = np.vstack([vertices1, vertices2])
    min_coords = np.min(all_vertices, axis=0)
    
    log_message(f"坐标偏移量: X={min_coords[0]:.2f}, Y={min_coords[1]:.2f}, Z={min_coords[2]:.2f}")
    
    # 平移到原点附近
    vertices1_norm = vertices1 - min_coords
    vertices2_norm = vertices2 - min_coords
    
    log_message("坐标标准化完成")
    return vertices1_norm, vertices2_norm, min_coords

def create_pyvista_mesh(vertices, faces):
    """创建PyVista网格"""
    try:
        import pyvista as pv
        
        # 创建面数组，PyVista格式需要在每个面前加上点数
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        faces_array = np.array(faces_with_header)
        
        mesh = pv.PolyData(vertices, faces_array)
        return mesh
    except ImportError:
        log_message("警告: PyVista未安装，无法使用PyVista方法")
        return None
    except Exception as e:
        log_message(f"创建PyVista网格失败: {e}")
        return None

def preprocess_mesh_with_pymeshlab(vertices, faces, temp_dir):
    """使用PyMeshLab预处理网格"""
    try:
        import pymeshlab
        
        # 保存为临时OBJ文件
        temp_obj = os.path.join(temp_dir, "temp_mesh.obj")
        write_obj_file(temp_obj, vertices, faces)
        
        # 使用pymeshlab加载和预处理
        ms = pymeshlab.MeshSet()
        ms.load_new_mesh(temp_obj)
        
        log_message("执行网格预处理...")
        
        # 基础清理
        ms.meshing_remove_duplicate_vertices()
        ms.meshing_remove_duplicate_faces()
        
        # 修复非流形
        try:
            ms.meshing_repair_non_manifold_edges()
            ms.meshing_repair_non_manifold_vertices()
        except Exception as e:
            log_message(f"非流形修复警告: {e}")
        
        # 统一法向
        try:
            ms.meshing_re_orient_faces_coherently()
        except Exception as e:
            log_message(f"法向统一警告: {e}")
        
        # 保存预处理后的网格
        processed_obj = os.path.join(temp_dir, "processed_mesh.obj")
        ms.save_current_mesh(processed_obj)
        
        # 清理临时文件
        if os.path.exists(temp_obj):
            os.remove(temp_obj)
        
        log_message("网格预处理完成")
        return processed_obj
        
    except Exception as e:
        log_message(f"网格预处理失败: {e}")
        return None

def write_obj_file(file_path, vertices, faces):
    """写入OBJ文件"""
    try:
        with open(file_path, 'w') as f:
            # 写入顶点
            for v in vertices:
                f.write(f"v {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            
            # 写入面（OBJ索引从1开始）
            for face in faces:
                f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
        
        return True
    except Exception as e:
        log_message(f"写入OBJ文件失败: {e}")
        return False

def write_ts_file(file_path, vertices, faces, name="mesh"):
    """写入TS文件"""
    try:
        with open(file_path, 'w') as f:
            # 写入TS文件头
            f.write(f"GOCAD TSURF 1\nHEADER {{\nname: {name}\n}}\n")
            f.write("TFACE\n")
            
            # 写入顶点(TS使用1-based索引)
            for i, v in enumerate(vertices):
                f.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            
            # 写入面(TS索引从1开始)
            for face in faces:
                f.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            # 写入结束标记
            f.write("END\n")
        
        return True
    except Exception as e:
        log_message(f"写入TS文件失败: {e}")
        return False

def try_pymeshlab_boolean_operations(file1, file2, output_dir, coord_offset=None):
    """
    使用PyMeshLab尝试布尔运算
    """
    try:
        import pymeshlab

        log_message("=== 尝试PyMeshLab布尔运算 ===")

        results = {}
        operations = [
            ("intersection", "交集"),
            ("difference", "差集"),
            ("union", "并集")
        ]

        for op_type, op_name in operations:
            try:
                log_message(f"执行{op_name}运算...")

                # 创建新的MeshSet
                ms = pymeshlab.MeshSet()
                ms.load_new_mesh(file1)
                ms.load_new_mesh(file2)

                # 记录输入网格信息
                ms.set_current_mesh(0)
                m1 = ms.current_mesh()
                log_message(f"  网格1: 顶点={len(m1.vertex_matrix())}, 面={len(m1.face_matrix())}")

                ms.set_current_mesh(1)
                m2 = ms.current_mesh()
                log_message(f"  网格2: 顶点={len(m2.vertex_matrix())}, 面={len(m2.face_matrix())}")

                # 执行布尔运算
                ms.set_current_mesh(0)
                if op_type == "intersection":
                    ms.generate_boolean_intersection(first_mesh=0, second_mesh=1)
                elif op_type == "difference":
                    ms.generate_boolean_difference(first_mesh=0, second_mesh=1)
                elif op_type == "union":
                    ms.generate_boolean_union(first_mesh=0, second_mesh=1)

                # 删除第二个网格，只保留结果
                ms.set_current_mesh(1)
                ms.delete_current_mesh()

                # 检查结果
                result_mesh = ms.current_mesh()
                vertex_count = len(result_mesh.vertex_matrix())
                face_count = len(result_mesh.face_matrix())

                log_message(f"  {op_name}结果: 顶点={vertex_count}, 面={face_count}")

                if vertex_count > 0 and face_count > 0:
                    # 保存结果
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    obj_file = os.path.join(output_dir, f"pymeshlab_{op_type}_{timestamp}.obj")
                    ts_file = os.path.join(output_dir, f"pymeshlab_{op_type}_{timestamp}.ts")

                    # 保存OBJ格式
                    ms.save_current_mesh(obj_file)

                    # 如果需要坐标还原，读取结果并还原坐标后保存TS格式
                    if coord_offset is not None:
                        vertices, faces = read_obj_file(obj_file)
                        if len(vertices) > 0:
                            # 还原坐标
                            vertices_restored = vertices + coord_offset
                            write_ts_file(ts_file, vertices_restored, faces, f"{op_type}_result")
                            log_message(f"  已保存{op_name}结果（坐标已还原）: {os.path.basename(ts_file)}")
                    else:
                        # 直接转换为TS格式
                        vertices, faces = read_obj_file(obj_file)
                        if len(vertices) > 0:
                            write_ts_file(ts_file, vertices, faces, f"{op_type}_result")
                            log_message(f"  已保存{op_name}结果: {os.path.basename(ts_file)}")

                    results[op_type] = {
                        'success': True,
                        'obj_file': obj_file,
                        'ts_file': ts_file,
                        'vertices': vertex_count,
                        'faces': face_count
                    }
                else:
                    log_message(f"  警告: {op_name}结果为空")
                    results[op_type] = {'success': False, 'reason': '结果为空'}

            except Exception as e:
                log_message(f"  {op_name}运算失败: {e}")
                results[op_type] = {'success': False, 'reason': str(e)}

        return results

    except ImportError:
        log_message("错误: PyMeshLab未安装")
        return {}
    except Exception as e:
        log_message(f"PyMeshLab布尔运算整体失败: {e}")
        return {}

def read_obj_file(file_path):
    """读取OBJ文件"""
    vertices = []
    faces = []

    try:
        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('v '):
                    parts = line.split()
                    if len(parts) >= 4:
                        vertices.append([float(parts[1]), float(parts[2]), float(parts[3])])
                elif line.startswith('f '):
                    parts = line.split()
                    if len(parts) >= 4:
                        # OBJ索引从1开始，转换为0开始
                        face = []
                        for i in range(1, 4):
                            vertex_idx = int(parts[i].split('/')[0]) - 1
                            face.append(vertex_idx)
                        faces.append(face)

        return np.array(vertices), np.array(faces)
    except Exception as e:
        log_message(f"读取OBJ文件失败: {e}")
        return np.array([]), np.array([])

def try_pyvista_boolean_operations(vertices1, faces1, vertices2, faces2, output_dir, coord_offset=None):
    """
    使用PyVista尝试布尔运算
    """
    try:
        import pyvista as pv

        log_message("=== 尝试PyVista布尔运算 ===")

        # 创建PyVista网格
        mesh1 = create_pyvista_mesh(vertices1, faces1)
        mesh2 = create_pyvista_mesh(vertices2, faces2)

        if mesh1 is None or mesh2 is None:
            log_message("无法创建PyVista网格")
            return {}

        log_message(f"网格1: 顶点={mesh1.n_points}, 面={mesh1.n_cells}")
        log_message(f"网格2: 顶点={mesh2.n_points}, 面={mesh2.n_cells}")

        results = {}
        operations = [
            ("intersection", "交集", "boolean_intersection"),
            ("difference", "差集", "boolean_difference"),
            ("union", "并集", "boolean_union")
        ]

        for op_type, op_name, method_name in operations:
            try:
                log_message(f"执行{op_name}运算...")

                # 执行布尔运算
                method = getattr(mesh1, method_name)
                result_mesh = method(mesh2)

                if result_mesh.n_points > 0 and result_mesh.n_cells > 0:
                    log_message(f"  {op_name}结果: 顶点={result_mesh.n_points}, 面={result_mesh.n_cells}")

                    # 保存结果
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    obj_file = os.path.join(output_dir, f"pyvista_{op_type}_{timestamp}.obj")
                    ts_file = os.path.join(output_dir, f"pyvista_{op_type}_{timestamp}.ts")

                    # 保存OBJ格式
                    result_mesh.save(obj_file)

                    # 转换为TS格式
                    vertices = result_mesh.points
                    faces = result_mesh.faces.reshape(-1, 4)[:, 1:4]  # 去掉每行第一个数字

                    if coord_offset is not None:
                        vertices = vertices + coord_offset

                    write_ts_file(ts_file, vertices, faces, f"{op_type}_result")
                    log_message(f"  已保存{op_name}结果: {os.path.basename(ts_file)}")

                    results[op_type] = {
                        'success': True,
                        'obj_file': obj_file,
                        'ts_file': ts_file,
                        'vertices': result_mesh.n_points,
                        'faces': result_mesh.n_cells
                    }
                else:
                    log_message(f"  警告: {op_name}结果为空")
                    results[op_type] = {'success': False, 'reason': '结果为空'}

            except Exception as e:
                log_message(f"  {op_name}运算失败: {e}")
                results[op_type] = {'success': False, 'reason': str(e)}

        return results

    except ImportError:
        log_message("警告: PyVista未安装，跳过PyVista方法")
        return {}
    except Exception as e:
        log_message(f"PyVista布尔运算整体失败: {e}")
        return {}

def main():
    """主函数"""
    log_message("=== 改进的TS文件布尔运算测试 ===")

    # 检查文件是否存在
    file1 = "1_05-J2a_1.ts"
    file2 = "dixing2507101.ts"

    if not os.path.exists(file1):
        log_message(f"错误: 文件 {file1} 不存在")
        return False

    if not os.path.exists(file2):
        log_message(f"错误: 文件 {file2} 不存在")
        return False

    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"boolean_results_{timestamp}"
    temp_dir = f"temp_{timestamp}"

    try:
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(temp_dir, exist_ok=True)
        log_message(f"创建输出目录: {output_dir}")

        # 步骤1: 读取TS文件
        log_message("\n=== 步骤1: 读取TS文件 ===")
        vertices1, faces1 = read_tsurf_data(file1)
        vertices2, faces2 = read_tsurf_data(file2)

        if len(vertices1) == 0 or len(faces1) == 0:
            log_message(f"错误: 无法读取文件 {file1}")
            return False

        if len(vertices2) == 0 or len(faces2) == 0:
            log_message(f"错误: 无法读取文件 {file2}")
            return False

        # 步骤2: 分析坐标
        log_message("\n=== 步骤2: 坐标分析 ===")
        coord_info1 = analyze_mesh_coordinates(vertices1, "网格1")
        coord_info2 = analyze_mesh_coordinates(vertices2, "网格2")

        # 步骤3: 坐标标准化
        log_message("\n=== 步骤3: 坐标标准化 ===")
        vertices1_norm, vertices2_norm, coord_offset = normalize_coordinates(vertices1, vertices2)

        # 验证标准化效果
        log_message("标准化后的坐标分析:")
        analyze_mesh_coordinates(vertices1_norm, "标准化网格1")
        analyze_mesh_coordinates(vertices2_norm, "标准化网格2")

        # 步骤4: 保存标准化后的临时文件
        log_message("\n=== 步骤4: 保存临时文件 ===")
        temp_obj1 = os.path.join(temp_dir, "mesh1_normalized.obj")
        temp_obj2 = os.path.join(temp_dir, "mesh2_normalized.obj")

        write_obj_file(temp_obj1, vertices1_norm, faces1)
        write_obj_file(temp_obj2, vertices2_norm, faces2)
        log_message("已保存标准化后的临时OBJ文件")

        # 步骤5: 网格预处理
        log_message("\n=== 步骤5: 网格预处理 ===")
        processed_obj1 = preprocess_mesh_with_pymeshlab(vertices1_norm, faces1, temp_dir)
        processed_obj2 = preprocess_mesh_with_pymeshlab(vertices2_norm, faces2, temp_dir)

        # 选择使用预处理后的文件还是原始标准化文件
        final_obj1 = processed_obj1 if processed_obj1 else temp_obj1
        final_obj2 = processed_obj2 if processed_obj2 else temp_obj2

        # 步骤6: 尝试PyMeshLab布尔运算
        log_message("\n=== 步骤6: PyMeshLab布尔运算 ===")
        pymeshlab_results = try_pymeshlab_boolean_operations(
            final_obj1, final_obj2, output_dir, coord_offset
        )

        # 步骤7: 尝试PyVista布尔运算（作为备选）
        log_message("\n=== 步骤7: PyVista布尔运算 ===")
        pyvista_results = try_pyvista_boolean_operations(
            vertices1_norm, faces1, vertices2_norm, faces2, output_dir, coord_offset
        )

        # 步骤8: 结果总结
        log_message("\n=== 处理结果总结 ===")

        success_count = 0
        total_operations = 3  # intersection, difference, union

        log_message("PyMeshLab结果:")
        for op_type in ["intersection", "difference", "union"]:
            if op_type in pymeshlab_results:
                result = pymeshlab_results[op_type]
                if result['success']:
                    log_message(f"  ✓ {op_type}: 成功 (顶点={result['vertices']}, 面={result['faces']})")
                    success_count += 1
                else:
                    log_message(f"  ✗ {op_type}: 失败 - {result['reason']}")
            else:
                log_message(f"  ✗ {op_type}: 未执行")

        log_message("PyVista结果:")
        for op_type in ["intersection", "difference", "union"]:
            if op_type in pyvista_results:
                result = pyvista_results[op_type]
                if result['success']:
                    log_message(f"  ✓ {op_type}: 成功 (顶点={result['vertices']}, 面={result['faces']})")
                else:
                    log_message(f"  ✗ {op_type}: 失败 - {result['reason']}")
            else:
                log_message(f"  ✗ {op_type}: 未执行")

        # 清理临时文件
        try:
            import shutil
            shutil.rmtree(temp_dir)
            log_message(f"已清理临时目录: {temp_dir}")
        except Exception as e:
            log_message(f"清理临时目录失败: {e}")

        log_message(f"\n处理完成！结果保存在: {output_dir}")
        log_message(f"成功的操作数: {success_count}/{total_operations * 2}")

        return success_count > 0

    except Exception as e:
        log_message(f"主程序执行失败: {e}")
        log_message(traceback.format_exc())
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            log_message("程序执行成功！")
            sys.exit(0)
        else:
            log_message("程序执行失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        log_message("程序被用户中断")
        sys.exit(1)
    except Exception as e:
        log_message(f"程序异常退出: {e}")
        sys.exit(1)
