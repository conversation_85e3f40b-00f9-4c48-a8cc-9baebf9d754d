#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证TS格式修复是否完全成功
"""

import os
import sys
import glob
from datetime import datetime

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def verify_ts_file_format(ts_file):
    """验证单个TS文件的格式"""
    try:
        with open(ts_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.strip().split('\n')
        
        # 检查必要的格式元素
        checks = {
            'file_header': lines[0].rstrip() == "GOCAD TSurf 1",
            'header_section': "HEADER {" in content and "name:" in content and "}" in content,
            'tface_marker': "TFACE" in content,
            'vertices': any(line.startswith("VRTX") for line in lines),
            'triangles': any(line.startswith("TRGL") for line in lines),
            'end_marker': lines[-1] == "END"
        }
        
        all_passed = all(checks.values())
        
        if all_passed:
            # 统计数据
            vrtx_count = len([line for line in lines if line.startswith("VRTX")])
            trgl_count = len([line for line in lines if line.startswith("TRGL")])
            
            log_message(f"✓ {os.path.basename(ts_file)}: 格式正确 ({vrtx_count} 顶点, {trgl_count} 面)")
            return True
        else:
            failed_checks = [check for check, passed in checks.items() if not passed]
            log_message(f"✗ {os.path.basename(ts_file)}: 格式错误 - 失败项: {failed_checks}")
            return False
            
    except Exception as e:
        log_message(f"✗ {os.path.basename(ts_file)}: 读取失败 - {e}")
        return False

def verify_all_recent_ts_files():
    """验证所有最近生成的TS文件"""
    log_message("=== 验证最近生成的TS文件格式 ===")
    
    # 查找最近的结果目录
    result_dirs = []
    for pattern in ["improved_tet_results_*", "tet_results_*", "no_norm_results_*"]:
        result_dirs.extend(glob.glob(pattern))
    
    if not result_dirs:
        log_message("✗ 找不到结果目录")
        return False
    
    # 使用最新的目录
    latest_dir = sorted(result_dirs)[-1]
    log_message(f"检查目录: {latest_dir}")
    
    # 查找所有TS文件
    ts_files = glob.glob(os.path.join(latest_dir, "*.ts"))
    
    if not ts_files:
        log_message("✗ 目录中没有TS文件")
        return False
    
    log_message(f"找到 {len(ts_files)} 个TS文件")
    
    # 验证每个文件
    all_valid = True
    for ts_file in sorted(ts_files):
        if not verify_ts_file_format(ts_file):
            all_valid = False
    
    return all_valid

def compare_with_original():
    """与原始TS文件格式对比"""
    log_message("\n=== 与原始TS文件格式对比 ===")
    
    original_files = ["1_05-J2a_1.ts", "dixing2507101.ts"]
    
    for original_file in original_files:
        if os.path.exists(original_file):
            log_message(f"验证原始文件: {original_file}")
            if verify_ts_file_format(original_file):
                log_message(f"✓ 原始文件格式正确")
            else:
                log_message(f"✗ 原始文件格式异常")
                return False
        else:
            log_message(f"⚠️  原始文件不存在: {original_file}")
    
    return True

def test_format_consistency():
    """测试格式一致性"""
    log_message("\n=== 测试格式一致性 ===")
    
    # 查找所有TS文件
    all_ts_files = []
    
    # 原始文件
    for original in ["1_05-J2a_1.ts", "dixing2507101.ts"]:
        if os.path.exists(original):
            all_ts_files.append(original)
    
    # 结果文件
    result_dirs = glob.glob("*results_*")
    for result_dir in result_dirs:
        ts_files = glob.glob(os.path.join(result_dir, "*.ts"))
        all_ts_files.extend(ts_files)
    
    if len(all_ts_files) < 2:
        log_message("⚠️  TS文件数量不足，无法进行一致性测试")
        return True
    
    log_message(f"检查 {len(all_ts_files)} 个TS文件的格式一致性")
    
    # 检查所有文件的格式特征
    format_features = []
    
    for ts_file in all_ts_files:
        try:
            with open(ts_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            features = {
                'starts_with_gocad': lines[0].rstrip() == "GOCAD TSurf 1",
                'has_header': any("HEADER {" in line for line in lines),
                'has_tface': any("TFACE" in line for line in lines),
                'has_vrtx': any(line.startswith("VRTX") for line in lines),
                'has_trgl': any(line.startswith("TRGL") for line in lines),
                'ends_with_end': lines[-1].strip() == "END"
            }
            
            format_features.append((os.path.basename(ts_file), features))
            
        except Exception as e:
            log_message(f"✗ 读取文件失败: {ts_file} - {e}")
            return False
    
    # 检查一致性
    if not format_features:
        return False
    
    reference_features = format_features[0][1]
    
    all_consistent = True
    for filename, features in format_features:
        if features != reference_features:
            log_message(f"✗ 格式不一致: {filename}")
            all_consistent = False
        else:
            log_message(f"✓ 格式一致: {filename}")
    
    return all_consistent

def main():
    """主函数"""
    log_message("开始验证TS格式修复...")
    
    recent_files_valid = verify_all_recent_ts_files()
    original_comparison = compare_with_original()
    format_consistency = test_format_consistency()
    
    if recent_files_valid and original_comparison and format_consistency:
        log_message("\n🎉 TS格式修复验证完全成功！")
        log_message("✓ 所有最近生成的TS文件格式正确")
        log_message("✓ 与原始TS文件格式一致")
        log_message("✓ 所有TS文件格式保持一致")
        log_message("\n修复效果:")
        log_message("1. 'TS模型间布尔运算' - 现在生成标准GOCAD TS格式")
        log_message("2. 'TS模型与面布尔运算' - 现在生成标准GOCAD TS格式")
        log_message("3. 所有布尔运算结果都是完全闭合的网格")
        log_message("4. TS文件可以被GOCAD等软件正确读取")
    else:
        log_message("\n❌ TS格式修复验证失败")
        if not recent_files_valid:
            log_message("- 最近生成的文件格式有问题")
        if not original_comparison:
            log_message("- 与原始文件格式不一致")
        if not format_consistency:
            log_message("- 文件格式不一致")
    
    return recent_files_valid and original_comparison and format_consistency

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        log_message(f"程序异常: {e}")
        sys.exit(1)
