#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的"模型与面布尔运算"函数
基于PyVista的成功经验，专门处理大坐标值的TS文件
可以直接集成到现有的ui_integrated_w5.py中
"""

import os
import numpy as np
import re
from datetime import datetime

def read_tsurf_data_improved(file_path):
    """
    改进的TS文件读取函数
    基于成功的测试经验
    """
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                for line in file:
                    line = line.strip()
                    if not line:
                        continue
                    
                    if 'VRTX' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x]
                            if len(nums) >= 4:
                                vertex_id = int(nums[0])
                                x, y, z = float(nums[1]), float(nums[2]), float(nums[3])
                                vrtx.append([x, y, z])
                                vrtx_map[vertex_id] = current_idx
                                current_idx += 1
                        except:
                            continue
                    
                    elif 'TRGL' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x and x.isdigit()]
                            if len(nums) == 3:
                                v1 = vrtx_map[int(nums[0])]
                                v2 = vrtx_map[int(nums[1])]
                                v3 = vrtx_map[int(nums[2])]
                                trgl.append([v1, v2, v3])
                        except:
                            continue
            
            if len(vrtx) > 0 and len(trgl) > 0:
                return np.array(vrtx), np.array(trgl)
                
        except:
            continue
    
    return np.array([]), np.array([])

def normalize_coordinates_improved(vertices1, vertices2):
    """
    改进的坐标标准化函数
    """
    all_vertices = np.vstack([vertices1, vertices2])
    min_coords = np.min(all_vertices, axis=0)
    
    vertices1_norm = vertices1 - min_coords
    vertices2_norm = vertices2 - min_coords
    
    return vertices1_norm, vertices2_norm, min_coords

def write_ts_file_improved(file_path, vertices, faces, name="result"):
    """
    改进的TS文件写入函数
    """
    try:
        with open(file_path, 'w') as f:
            f.write(f"GOCAD TSURF 1\nHEADER {{\nname: {name}\n}}\nTFACE\n")
            for i, v in enumerate(vertices):
                f.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            for face in faces:
                f.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            f.write("END\n")
        return True
    except:
        return False

def create_pyvista_mesh_improved(vertices, faces):
    """
    创建PyVista网格的改进函数
    """
    try:
        import pyvista as pv
        
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        
        mesh = pv.PolyData(vertices, np.array(faces_with_header))
        return mesh
    except:
        return None

def perform_ts_boolean_operations_improved(self, checked=False):
    """
    改进的TS模型布尔运算函数
    基于PyVista的成功经验，可以处理大坐标值和非水密网格
    
    这个函数可以替换ui_integrated_w5.py中的perform_ts_surface_boolean_operations函数
    """
    try:
        self.ensure_output_folder()

        if not self.ts_model_for_surface_op_file or not self.ts_surface_file:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", "请先选择TS模型和TS面文件")
            return

        # 检查PyVista是否可用
        try:
            import pyvista as pv
            self.add_log_message(f"使用PyVista版本: {pv.__version__}")
        except ImportError:
            self.add_log_message("错误: PyVista未安装，无法执行布尔运算")
            QMessageBox.critical(self, "错误", "PyVista未安装。请安装PyVista: pip install pyvista")
            return

        self.add_log_message("=== 开始改进的TS模型布尔运算 ===")
        
        # 步骤1: 读取TS文件
        self.add_log_message("步骤1: 读取TS文件...")
        vertices1, faces1 = read_tsurf_data_improved(self.ts_model_for_surface_op_file)
        vertices2, faces2 = read_tsurf_data_improved(self.ts_surface_file)
        
        if len(vertices1) == 0 or len(faces1) == 0:
            self.add_log_message(f"错误: 无法读取TS模型文件 {self.ts_model_for_surface_op_file}")
            return
        
        if len(vertices2) == 0 or len(faces2) == 0:
            self.add_log_message(f"错误: 无法读取TS面文件 {self.ts_surface_file}")
            return
        
        self.add_log_message(f"TS模型: {len(vertices1)} 顶点, {len(faces1)} 面")
        self.add_log_message(f"TS面: {len(vertices2)} 顶点, {len(faces2)} 面")
        
        # 步骤2: 坐标分析和标准化
        self.add_log_message("步骤2: 坐标标准化...")
        max_coord1 = np.max(np.abs(vertices1))
        max_coord2 = np.max(np.abs(vertices2))
        
        self.add_log_message(f"TS模型最大坐标值: {max_coord1:.1f}")
        self.add_log_message(f"TS面最大坐标值: {max_coord2:.1f}")
        
        # 执行坐标标准化
        vertices1_norm, vertices2_norm, coord_offset = normalize_coordinates_improved(vertices1, vertices2)
        self.add_log_message(f"坐标偏移量: X={coord_offset[0]:.1f}, Y={coord_offset[1]:.1f}, Z={coord_offset[2]:.1f}")
        
        # 步骤3: 创建PyVista网格
        self.add_log_message("步骤3: 创建PyVista网格...")
        mesh1 = create_pyvista_mesh_improved(vertices1_norm, faces1)
        mesh2 = create_pyvista_mesh_improved(vertices2_norm, faces2)
        
        if mesh1 is None or mesh2 is None:
            self.add_log_message("错误: 无法创建PyVista网格")
            return
        
        self.add_log_message(f"网格1: {mesh1.n_points} 顶点, {mesh1.n_cells} 面")
        self.add_log_message(f"网格2: {mesh2.n_points} 顶点, {mesh2.n_cells} 面")
        
        # 步骤4: 执行布尔运算
        self.add_log_message("步骤4: 执行布尔运算...")
        
        operations = [
            ("intersection", "交集", "boolean_intersection"),
            ("difference", "差集", "boolean_difference"),
            ("union", "并集", "boolean_union")
        ]
        
        success_count = 0
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for op_type, op_name, method_name in operations:
            try:
                self.add_log_message(f"执行{op_name}运算...")
                
                # 执行布尔运算
                method = getattr(mesh1, method_name)
                result_mesh = method(mesh2)
                
                if result_mesh.n_points > 0 and result_mesh.n_cells > 0:
                    self.add_log_message(f"  ✓ {op_name}成功: {result_mesh.n_points} 顶点, {result_mesh.n_cells} 面")
                    
                    # 保存结果
                    obj_file = os.path.join(self.output_folder, f"improved_{op_type}_{timestamp}.obj")
                    ts_file = os.path.join(self.output_folder, f"improved_{op_type}_{timestamp}.ts")
                    
                    # 保存OBJ格式
                    result_mesh.save(obj_file)
                    
                    # 转换并保存TS格式
                    vertices = result_mesh.points
                    faces = result_mesh.faces.reshape(-1, 4)[:, 1:4]
                    
                    # 还原坐标
                    vertices_restored = vertices + coord_offset
                    
                    if write_ts_file_improved(ts_file, vertices_restored, faces, f"{op_type}_result"):
                        self.add_log_message(f"  已保存: {os.path.basename(ts_file)}")
                        success_count += 1
                    else:
                        self.add_log_message(f"  警告: 保存TS文件失败")
                else:
                    self.add_log_message(f"  ✗ {op_name}结果为空")
                    
            except Exception as e:
                self.add_log_message(f"  ✗ {op_name}失败: {e}")
        
        # 步骤5: 结果总结
        self.add_log_message("=== 布尔运算完成 ===")
        self.add_log_message(f"成功的操作: {success_count}/3")
        
        if success_count > 0:
            self.add_log_message("🎉 布尔运算成功！结果已保存到输出目录。")
            self.statusBar.showMessage(f"布尔运算完成，成功 {success_count}/3 个操作")
        else:
            self.add_log_message("❌ 所有布尔运算都失败了。")
            self.statusBar.showMessage("布尔运算失败")
        
        return success_count > 0
        
    except Exception as e:
        self.add_log_message(f"改进的布尔运算执行失败: {e}")
        import traceback
        self.add_log_message(traceback.format_exc())
        self.statusBar.showMessage("布尔运算失败")
        return False
