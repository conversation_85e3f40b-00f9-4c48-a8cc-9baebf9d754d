#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证完整修复：TS格式 + 挤出实体形态保持
"""

import os
import sys
import glob
from datetime import datetime

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def verify_ts_format():
    """验证TS格式修复"""
    log_message("=== 验证TS格式修复 ===")
    
    # 检查最新的结果目录
    result_dirs = glob.glob("*results_*")
    if not result_dirs:
        log_message("✗ 没有找到结果目录")
        return False
    
    latest_dir = sorted(result_dirs)[-1]
    ts_files = glob.glob(os.path.join(latest_dir, "*.ts"))

    # 如果最新目录没有TS文件，检查其他目录
    if not ts_files:
        for result_dir in sorted(result_dirs, reverse=True):
            ts_files = glob.glob(os.path.join(result_dir, "*.ts"))
            if ts_files:
                latest_dir = result_dir
                break
    
    if not ts_files:
        log_message("✗ 没有找到TS文件")
        return False
    
    log_message(f"检查目录: {latest_dir}")
    log_message(f"找到 {len(ts_files)} 个TS文件")
    
    all_valid = True
    for ts_file in ts_files:
        try:
            with open(ts_file, 'r', encoding='utf-8') as f:
                first_line = f.readline().rstrip()
            
            if first_line == "GOCAD TSurf 1":
                log_message(f"✓ {os.path.basename(ts_file)}: TS格式正确")
            else:
                log_message(f"✗ {os.path.basename(ts_file)}: TS格式错误")
                all_valid = False
        except Exception as e:
            log_message(f"✗ {os.path.basename(ts_file)}: 读取失败 - {e}")
            all_valid = False
    
    return all_valid

def verify_workflow_steps():
    """验证工作流程步骤"""
    log_message("\n=== 验证工作流程步骤 ===")
    
    # 检查代码中的步骤顺序
    ui_file = "ui_integrated_w5.py"
    if not os.path.exists(ui_file):
        log_message("✗ UI文件不存在")
        return False
    
    with open(ui_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键步骤
    steps_found = []
    
    if "步骤3: 保存原始挤出实体" in content:
        steps_found.append("保存原始挤出实体")
        log_message("✓ 找到'保存原始挤出实体'步骤")
    
    if "步骤4: 执行四面体网格化" in content:
        steps_found.append("四面体网格化")
        log_message("✓ 找到'四面体网格化'步骤")
    
    if "步骤5: 执行PyMeshLab布尔运算" in content:
        steps_found.append("布尔运算")
        log_message("✓ 找到'布尔运算'步骤")
    
    # 检查是否删除了重复的保存步骤
    save_count = content.count("保存原始挤出实体")
    if save_count == 1:
        log_message("✓ 挤出实体保存步骤唯一")
    else:
        log_message(f"✗ 发现 {save_count} 个挤出实体保存步骤")
        return False
    
    # 检查步骤顺序是否正确
    expected_steps = ["保存原始挤出实体", "四面体网格化", "布尔运算"]
    if steps_found == expected_steps:
        log_message("✓ 工作流程步骤顺序正确")
        return True
    else:
        log_message(f"✗ 工作流程步骤顺序错误: {steps_found}")
        return False

def verify_extruded_entity_handling():
    """验证挤出实体处理"""
    log_message("\n=== 验证挤出实体处理 ===")
    
    ui_file = "ui_integrated_w5.py"
    with open(ui_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键代码片段
    checks = {
        "清理挤出实体": "extruded_solid_clean = extruded_solid.clean().triangulate()" in content,
        "保存原始形态": "extruded_solid_clean.save(extruded_obj)" in content,
        "四面体化分离": "仅用于布尔运算" in content,
        "标准TS格式": "write_ts_file" in content and "GOCAD TSurf 1" in content
    }
    
    all_passed = True
    for check_name, passed in checks.items():
        if passed:
            log_message(f"✓ {check_name}: 正确实现")
        else:
            log_message(f"✗ {check_name}: 未找到或实现错误")
            all_passed = False
    
    return all_passed

def verify_module_deletion():
    """验证模块删除"""
    log_message("\n=== 验证模块删除 ===")
    
    ui_file = "ui_integrated_w5.py"
    with open(ui_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否删除了不闭合模块
    deleted_items = {
        "不闭合按钮": "btn_boolean_ts_surface_pyvista" not in content,
        "不闭合函数": "perform_ts_surface_boolean_operations_pyvista" not in content,
        "不闭合布局": content.count("btn_boolean_ts_surface_pyvista") == 0
    }
    
    all_deleted = True
    for item_name, deleted in deleted_items.items():
        if deleted:
            log_message(f"✓ {item_name}: 已删除")
        else:
            log_message(f"✗ {item_name}: 仍然存在")
            all_deleted = False
    
    # 检查保留的功能
    retained_items = {
        "主要布尔运算": "btn_boolean_ts_surface" in content,
        "四面体网格化": "create_tetrahedral_mesh" in content,
        "PyMeshLab布尔运算": "perform_tetrahedral_boolean_operations" in content
    }
    
    all_retained = True
    for item_name, retained in retained_items.items():
        if retained:
            log_message(f"✓ {item_name}: 已保留")
        else:
            log_message(f"✗ {item_name}: 意外删除")
            all_retained = False
    
    return all_deleted and all_retained

def main():
    """主函数"""
    log_message("开始验证完整修复...")
    
    ts_format_ok = verify_ts_format()
    workflow_ok = verify_workflow_steps()
    extruded_ok = verify_extruded_entity_handling()
    deletion_ok = verify_module_deletion()
    
    if ts_format_ok and workflow_ok and extruded_ok and deletion_ok:
        log_message("\n🎉 完整修复验证成功！")
        log_message("✓ TS格式修复完成 - 生成标准GOCAD格式")
        log_message("✓ 挤出实体形态保持修复完成 - 保持原始高质量形态")
        log_message("✓ 工作流程优化完成 - 步骤顺序合理")
        log_message("✓ 不闭合模块删除完成 - 界面简化")
        log_message("\n现在的功能状态:")
        log_message("1. 'TS模型间布尔运算' - 四面体网格化 + 标准TS格式")
        log_message("2. 'TS模型与面布尔运算' - 四面体网格化 + 标准TS格式 + 形态保持")
        log_message("3. 所有结果都是完全闭合的高质量网格")
        log_message("4. 挤出实体保持原始形态不变")
        log_message("5. 可被GOCAD等专业软件正确读取")
    else:
        log_message("\n❌ 完整修复验证失败")
        if not ts_format_ok:
            log_message("- TS格式修复有问题")
        if not workflow_ok:
            log_message("- 工作流程有问题")
        if not extruded_ok:
            log_message("- 挤出实体处理有问题")
        if not deletion_ok:
            log_message("- 模块删除有问题")
    
    return ts_format_ok and workflow_ok and extruded_ok and deletion_ok

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        log_message(f"程序异常: {e}")
        sys.exit(1)
