#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyVista专用TS文件布尔运算测试脚本
专门处理1_05-J2a_1.ts和dixing2507101.ts的布尔运算
PyVista对非水密网格更宽容，适合处理这类问题
"""

import os
import sys
import numpy as np
import re
from datetime import datetime
import traceback

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def read_tsurf_data(file_path):
    """读取TS文件数据"""
    log_message(f"读取TS文件: {os.path.basename(file_path)}")
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                for line in file:
                    line = line.strip()
                    if not line:
                        continue
                    
                    if 'VRTX' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x]
                            if len(nums) >= 4:
                                vertex_id = int(nums[0])
                                x, y, z = float(nums[1]), float(nums[2]), float(nums[3])
                                vrtx.append([x, y, z])
                                vrtx_map[vertex_id] = current_idx
                                current_idx += 1
                        except:
                            continue
                    
                    elif 'TRGL' in line:
                        l_input = re.split(r'[\s*]', line)
                        try:
                            nums = [x for x in l_input[1:] if x and x.isdigit()]
                            if len(nums) == 3:
                                v1 = vrtx_map[int(nums[0])]
                                v2 = vrtx_map[int(nums[1])]
                                v3 = vrtx_map[int(nums[2])]
                                trgl.append([v1, v2, v3])
                        except:
                            continue
            
            if len(vrtx) > 0 and len(trgl) > 0:
                log_message(f"  成功: {len(vrtx)} 顶点, {len(trgl)} 面")
                return np.array(vrtx), np.array(trgl)
                
        except:
            continue
    
    return np.array([]), np.array([])

def normalize_coordinates(vertices1, vertices2):
    """坐标标准化到原点附近"""
    all_vertices = np.vstack([vertices1, vertices2])
    min_coords = np.min(all_vertices, axis=0)
    
    vertices1_norm = vertices1 - min_coords
    vertices2_norm = vertices2 - min_coords
    
    log_message(f"坐标偏移: X={min_coords[0]:.1f}, Y={min_coords[1]:.1f}, Z={min_coords[2]:.1f}")
    return vertices1_norm, vertices2_norm, min_coords

def write_ts_file(file_path, vertices, faces, name="result"):
    """写入TS文件"""
    try:
        with open(file_path, 'w') as f:
            f.write(f"GOCAD TSURF 1\nHEADER {{\nname: {name}\n}}\nTFACE\n")
            for i, v in enumerate(vertices):
                f.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            for face in faces:
                f.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            f.write("END\n")
        return True
    except Exception as e:
        log_message(f"写入TS文件失败: {e}")
        return False

def create_pyvista_mesh(vertices, faces):
    """创建PyVista网格"""
    try:
        import pyvista as pv
        
        # 创建面数组，PyVista格式需要在每个面前加上点数
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        
        mesh = pv.PolyData(vertices, np.array(faces_with_header))
        return mesh
    except Exception as e:
        log_message(f"创建PyVista网格失败: {e}")
        return None

def perform_boolean_operations(mesh1, mesh2, output_dir, coord_offset):
    """执行PyVista布尔运算"""
    log_message("=== 执行PyVista布尔运算 ===")
    
    results = {}
    operations = [
        ("intersection", "交集"),
        ("difference", "差集"),
        ("union", "并集")
    ]
    
    for op_type, op_name in operations:
        try:
            log_message(f"执行{op_name}...")
            
            # 执行布尔运算
            if op_type == "intersection":
                result_mesh = mesh1.boolean_intersection(mesh2)
            elif op_type == "difference":
                result_mesh = mesh1.boolean_difference(mesh2)
            elif op_type == "union":
                result_mesh = mesh1.boolean_union(mesh2)
            
            # 检查结果
            if result_mesh.n_points > 0 and result_mesh.n_cells > 0:
                log_message(f"  ✓ 成功: {result_mesh.n_points} 顶点, {result_mesh.n_cells} 面")
                
                # 保存结果
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                obj_file = os.path.join(output_dir, f"{op_type}_{timestamp}.obj")
                ts_file = os.path.join(output_dir, f"{op_type}_{timestamp}.ts")
                
                # 保存OBJ格式
                result_mesh.save(obj_file)
                
                # 转换并保存TS格式
                vertices = result_mesh.points
                faces = result_mesh.faces.reshape(-1, 4)[:, 1:4]  # 去掉每行第一个数字
                
                # 还原坐标
                vertices_restored = vertices + coord_offset
                
                if write_ts_file(ts_file, vertices_restored, faces, f"{op_type}_result"):
                    log_message(f"  已保存: {os.path.basename(ts_file)}")
                    results[op_type] = {
                        'success': True,
                        'obj_file': obj_file,
                        'ts_file': ts_file,
                        'vertices': result_mesh.n_points,
                        'faces': result_mesh.n_cells
                    }
                else:
                    results[op_type] = {'success': False, 'reason': '保存失败'}
            else:
                log_message(f"  ✗ {op_name}结果为空")
                results[op_type] = {'success': False, 'reason': '结果为空'}
                
        except Exception as e:
            log_message(f"  ✗ {op_name}失败: {e}")
            results[op_type] = {'success': False, 'reason': str(e)}
    
    return results

def main():
    """主函数"""
    log_message("=== PyVista TS文件布尔运算测试 ===")
    
    # 检查依赖
    try:
        import pyvista as pv
        log_message(f"PyVista版本: {pv.__version__}")
    except ImportError:
        log_message("错误: PyVista未安装")
        return False
    
    # 检查输入文件
    file1 = "1_05-J2a_1.ts"
    file2 = "dixing2507101.ts"
    
    if not os.path.exists(file1):
        log_message(f"错误: 找不到文件 {file1}")
        return False
    
    if not os.path.exists(file2):
        log_message(f"错误: 找不到文件 {file2}")
        return False
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"pyvista_results_{timestamp}"
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        log_message(f"输出目录: {output_dir}")
        
        # 步骤1: 读取TS文件
        log_message("\n=== 步骤1: 读取文件 ===")
        vertices1, faces1 = read_tsurf_data(file1)
        vertices2, faces2 = read_tsurf_data(file2)
        
        if len(vertices1) == 0 or len(vertices2) == 0:
            log_message("错误: 无法读取输入文件")
            return False
        
        # 步骤2: 坐标标准化
        log_message("\n=== 步骤2: 坐标标准化 ===")
        vertices1_norm, vertices2_norm, coord_offset = normalize_coordinates(vertices1, vertices2)
        
        # 步骤3: 创建PyVista网格
        log_message("\n=== 步骤3: 创建网格 ===")
        mesh1 = create_pyvista_mesh(vertices1_norm, faces1)
        mesh2 = create_pyvista_mesh(vertices2_norm, faces2)
        
        if mesh1 is None or mesh2 is None:
            log_message("错误: 无法创建PyVista网格")
            return False
        
        log_message(f"网格1: {mesh1.n_points} 顶点, {mesh1.n_cells} 面")
        log_message(f"网格2: {mesh2.n_points} 顶点, {mesh2.n_cells} 面")
        
        # 步骤4: 执行布尔运算
        log_message("\n=== 步骤4: 布尔运算 ===")
        results = perform_boolean_operations(mesh1, mesh2, output_dir, coord_offset)
        
        # 步骤5: 结果总结
        log_message("\n=== 结果总结 ===")
        success_count = 0
        
        for op_type in ["intersection", "difference", "union"]:
            if op_type in results and results[op_type]['success']:
                result = results[op_type]
                log_message(f"✓ {op_type}: 成功 (顶点={result['vertices']}, 面={result['faces']})")
                success_count += 1
            else:
                reason = results.get(op_type, {}).get('reason', '未执行')
                log_message(f"✗ {op_type}: 失败 - {reason}")
        
        log_message(f"\n处理完成！成功: {success_count}/3")
        log_message(f"结果保存在: {output_dir}")
        
        if success_count > 0:
            log_message("\n🎉 至少有一些布尔运算成功了！")
            log_message("请检查输出目录中的TS和OBJ文件。")
        else:
            log_message("\n❌ 所有布尔运算都失败了。")
        
        return success_count > 0
        
    except Exception as e:
        log_message(f"程序执行失败: {e}")
        log_message(traceback.format_exc())
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log_message("程序被用户中断")
        sys.exit(1)
